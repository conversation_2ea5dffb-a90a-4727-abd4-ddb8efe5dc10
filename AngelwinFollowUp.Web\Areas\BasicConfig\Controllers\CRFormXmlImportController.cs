﻿using Microsoft.AspNetCore.Mvc;
using System.Data;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore;
using AngelwinFollowUp.Models;
using Common.DataSourceSupport;
using AngelwinFollowUp.Web.Areas.BasicConfig.Models;

namespace AngelwinFollowUp.Web.Areas.BasicConfig.Controllers
{
    //[Authorizing]
    [Area("BasicConfig")]
    public class CRFormXmlImportController : Controller
    {
        private readonly AngelwinFollowUpDbContext db;
        private readonly IConfiguration configuration;

        public CRFormXmlImportController(AngelwinFollowUpDbContext _db, IConfiguration _configuration)
        {
            db = _db;
            configuration = _configuration;
        }

        public ActionResult Index()
        {
            return View();
        }


        //TODO20250710
        public IActionResult GetFormList(int deptId)
        {
            //try
            //{
            //    var query = db.CRForms.Where(o => o.LevelType == 2).AsQueryable();
            //    if (deptId > 0)
            //    {
            //        query = query.Where(o => o.HospitalDeptId == deptId).AsQueryable();
            //    }
            //    var list = query.Select(o => new { Id = o.Id, Name =$"{ o.FormName} [{o.FormId}]" })
            //        .Distinct().ToList();
            //    var setting = new JsonSerializerSettings();
            //    setting.ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver();//json字符串大小写原样输出
            //    return Json(new { code = "0", msg = "成功", data = list }, setting);
            //}
            //catch (Exception ex)
            //{
            //    return Json(new { code = "-100", msg = "数据查询失败！", data = ex });
            //}
            return null;
        }


        public ActionResult CheckExists(string crformId)
        {
            try
            {
                var queryCount = db.CRFormFieldSets.Where(d => d.FormId == crformId).Count();
                var setting = new JsonSerializerSettings();
                setting.ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver();//json字符串大小写原样输出
                return Json(new { code = "0", msg = "成功", data = queryCount }, setting);
            }
            catch (Exception ex)
            {
                return Json(new { code = "-100", msg = "数据查询失败！", data = ex });
            }
        }

        // POST: CRFormXmlImportController/Create
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public ActionResult Create(int crformId)
        {
            string hanaConn = configuration["AppSettings:DmsConnectionString"]??"";
            var outfacMsg = string.Empty;
            var errMsg = string.Empty;
            try
            {
                if (crformId == 0)
                {
                    return Json(new { okMsg = "请检查必填项" });
                }
                var formId = db.CRForms.FirstOrDefault(d => d.Id == crformId)?.FormId;
                #region 获取hana数据导入
                string query = @"SELECT XML_UID, NAME, XML_TYPE, XML_TEXT, CREATE_TIME from dms.DMS_CONFIG_XML where XML_UID =@formId;";
                var OutputDict = new Dictionary<string, string>();
                var paramList = new List<CommonParamDTO>();
                paramList.Add(new CommonParamDTO { Name = "@formId", Values = formId });
                var xmlData = DataSourceFactory.GetMyFactory("MYSQL", out outfacMsg)?.CreateContext()?
                       .GetList<DTO>(CommandType.Text, hanaConn, query, paramList, out OutputDict, out errMsg);

                var msg = "";
                if (xmlData != null && xmlData.Any())
                {
                    // 获取XML文本内容
                    var xmlText = xmlData.FirstOrDefault()?.XML_TEXT;
                    List<(string, string)> dataTuList = new List<(string, string)>();
                    if (string.IsNullOrEmpty(xmlText))
                    {
                        return Json(new { okMsg = $"xml无数据！" });
                    }
                    #region 解析xml
                    try
                    {
                        // 解析XML
                        System.Xml.Linq.XDocument doc = System.Xml.Linq.XDocument.Parse(xmlText);

                        // 提取表格下的所有column元素
                        var columns = doc.Descendants("column")
                               .Where(c => c.Attribute("name") != null
                               && !string.IsNullOrEmpty(c.Attribute("name")?.Value));

                        // 创建字段列表
                        int idx = 0;
                        foreach (var column in columns)
                        {
                            var name = column?.Attribute("name")?.Value ?? "";
                            var formNameDesc = column?.Attribute("formNameDesc")?.Value ?? "";
                            if (name == "ID")//排除ID
                            {
                                continue;
                            }
                            idx++;
                            dataTuList.Add((name, formNameDesc));
                        }
                    }
                    catch (Exception ex)
                    {
                        return Json(new { okMsg = $"XML解析失败: {ex.Message}" });
                    }
                    #endregion

                    var i = 0;
                    var crformList = db.CRFormFieldSets.Where(d => d.FormId == formId).ToList();
                    var addItemList = new List<CRFormFieldSet>();
                    var updateList = new List<CRFormFieldSet>();
                    var delList = new List<CRFormFieldSet>();  //暂不处理
                    foreach (var (fieldName, fieldComment) in dataTuList)
                    {
                        i++;
                        var up_Item = crformList.FirstOrDefault(o => o.FieldName == fieldName);
                        if (up_Item != null)
                        {
                            up_Item.FieldComment = fieldComment;
                            up_Item.Orderby = i;
                            updateList.Add(up_Item);
                        }
                        else
                        {
                            CRFormFieldSet cs = new CRFormFieldSet
                            {
                                FieldName = fieldName,
                                FieldComment = fieldComment,
                                Orderby = i,
                                CreatedTime = DateTime.Now,
                                CreateUserName = User?.Identity?.Name??"Sys"
                            };
                            addItemList.Add(cs);
                        }
                    }

                    if (updateList != null && updateList.Any())
                    {
                        msg += $"修改【{updateList.Count()}】条变量的描述；";
                        db.SaveChanges();
                    }

                    if (addItemList != null && addItemList.Any())
                    {
                        db.CRFormFieldSets.AddRangeAsync(addItemList);
                        db.SaveChanges();
                        msg += $"新增了【{addItemList.Count()}】条变量记录；";
                    }
                }
                else
                {
                    return Json(new { okMsg = $"hana无数据或连接hana报错:{outfacMsg}-{errMsg}" });
                }
                #endregion
                //return RedirectToAction(nameof(Index));
                return Json(new { okMsg = $"导入成功：{msg}！" });
            }
            catch (Exception ex)
            {
                return Json(new { errorMsg = "导入时数据出错。", data = ex });
            }
        }
    }
}
