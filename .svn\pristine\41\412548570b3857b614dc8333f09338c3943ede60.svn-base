﻿using AngelwinFollowUp.Models;
using AngelwinFollowUp.Web.Filters;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using static AngelwinFollowUp.Web.Areas.FollowUpManage.Controllers.FollowUpManageSearchController;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using static System.Runtime.InteropServices.JavaScript.JSType;
namespace AngelwinFollowUp.Web.Areas.FollowUpManage.Controllers
{

    [Authorizing]
    [Area("FollowUpManage")]
    public class FollowUpManageSearchController:Controller
    {

        private IConfiguration Configuration { get; }

        private readonly AngelwinFollowUpDbContext db;
        public FollowUpManageSearchController(IConfiguration configuration, AngelwinFollowUpDbContext _db)
        {
            Configuration = configuration;
            db = _db;

        }
        public IActionResult Index()
        {
            ViewBag.BeginDate =null;
            ViewBag.endDate =null;
            return View();
        }
        
        public  string GetStatus(int xStatus)
        {
            if (xStatus == 0)
            {
                return "待随访";
            }
            else if (xStatus == 1)
            {
                return "已随访";
            }
            else
            {
                return "失访";
            }
        }

        public class Ids
        {
            public  int TemplateId { get; set; }
            public int  TemplateDetailId { get; set; }
        }
        public  string GetTemplateDetailId(List<Ids> ids, int  id)
        {
           return  string.Join(",", ids.Where(s => s.TemplateId == id).Select(s => s.TemplateDetailId).ToList());
        }

        public string GetPatientSource(string xType)
        {
            if (xType == "I")
            {
                return "住院";
            }
            else if (xType == "O")
            {
                return "门诊";
            }
            else if (xType == "P")
            {
                return "体检";
            }
            else
                return "其他";
        }
        public ActionResult GetFollowupTemplateDetailsList(string ids)
        {
            var mIds = ids.Split(',');
            var mFollowupPlansList = db.FollowupPlans.Where(p => ids.Contains(p.TemplateDetailId.ToString()))
                .Select(p => new
                {
                    p.Status,
                    p.TemplateDetailId
                }).ToList();

            var query = db.FollowupTemplateDetails.Where(p => mIds.Contains(p.Id.ToString())).OrderByDescending(p=>p.CreatedTime).ToList();

            JObject json = new JObject(
                new JProperty("code", "0"),
                new JProperty("msg", "成功"),
                new JProperty("count", query.Count()),
                new JProperty("data",
                   new JArray(
                 from r in query
                 select new JObject(
                           new JProperty("TemplateDetailName", r.TemplateDetailName),
                          new JProperty("Methods", r.Methods),
                           new JProperty("Status", GetStatus(mFollowupPlansList.FirstOrDefault(p=>p.TemplateDetailId==r.Id).Status)),
                           new JProperty("CreatedTime", r.CreatedTime.ToString("yyyy-MM-dd")),
                           new JProperty("Description", r.Description)
                            ))));
            return Ok(json.ToString());
        }
        public ActionResult GetFollowupList(int page, int limit, string PatientName,string PatientSource,string Status, string KeyWords, DateTime? BeginDate, DateTime? endDate)
        {

            var query = db.FollowupPlans.Include(p => p.FollowupTemplate).Include(p => p.ResearchPatient).ThenInclude(p => p.HospitalDept).AsQueryable();
            if (!string.IsNullOrWhiteSpace(PatientName))
            {
                PatientName = PatientName.Trim();
                query = query.Where(i => i.ResearchPatient.PatientName.Contains(PatientName));
            }


            if (!string.IsNullOrWhiteSpace(PatientSource))
            {
                PatientSource = PatientSource.Trim();
                query = query.Where(i => i.ResearchPatient.PatientSource == PatientSource);
            }

            if (!string.IsNullOrWhiteSpace(Status))
            {

                query = query.Where(i => i.Status == Convert.ToInt32(Status));
            }

            if (!string.IsNullOrWhiteSpace(KeyWords))
            {
                KeyWords = KeyWords.Trim();
                query = query.Where(i => i.ResearchPatient.Telephone.Contains(KeyWords) || i.ResearchPatient.IDCardNo.Contains(KeyWords));
            }

            if (BeginDate != null)
            {

                query = query.Where(w => w.CreatedTime >= BeginDate);
            }
            if (endDate != null)
            {
                endDate = endDate.Value.AddDays(1);
                query = query.Where(w => w.CreatedTime < endDate);
            }

              var  mTempList = query.Select(p => new
                 {
                         TemplateId = p.TemplateId,
                         TemplateDetailId= p.TemplateDetailId
     
              }).ToList();
        

          var   mListQuery= query.GroupBy(p => new { p.TemplateId,p.PatientId })
                           .Select(p => new
                           {
                               TemplateId =p.Key.TemplateId,
                               PId=p.Key.PatientId,
                               TemplateName=p.First().FollowupTemplate.TemplateName,
                               DeptName = p.First().ResearchPatient.HospitalDept.Name,
                               PatientId = p.First().ResearchPatient.PatientId,
                               p.First().ResearchPatient.IDCardNo,
                               p.First().ResearchPatient.PatientSource,
                               p.First().ResearchPatient.PatientName,
                               p.First().ResearchPatient.Sex,
                               p.First().ResearchPatient.Age,
                               p.First().ResearchPatient.Telephone,
                               p.First().ResearchPatient.Diagnosis,
                               p.First().Description,
                             //  p.First().Status,
                             //  StatusTime = p.First().StatusTime.HasValue ? p.First().StatusTime.Value.ToString("yyyy-MM-dd") : "",
                               CreatedTime = p.First().CreatedTime,
                               FollowupCounts= p.Sum(q => q.Status) + "/"  +p.Count(q=>q.TemplateDetailId>0)
                           }).AsQueryable();
                           
                     
           

     


    


              var  mList = mListQuery.OrderByDescending(o => o.CreatedTime).Skip((page - 1) * limit).Take(limit).ToList();
                    JObject json = new JObject(
                           new JProperty("code", "0"),
                           new JProperty("msg", "成功"),
                           new JProperty("count", mList.Count()),
                           new JProperty("data",
                              new JArray(
                            from r in mList
                            select new JObject(
                                      new JProperty("TemplateId", r.TemplateId),
                                      new JProperty("PId", r.PId),
                                       new JProperty("TemplateName", r.TemplateName),
                                      new JProperty("TemplateDetailId", string.Join(",", mTempList.Where(s => s.TemplateId == r.TemplateId).Select(s => s.TemplateDetailId).ToList())),
                                      new JProperty("FollowupCounts", r.FollowupCounts),
                                      new JProperty("DeptName", r.DeptName),
                                       new JProperty("PatientId", r.PatientId),
                                       new JProperty("PatientSource", GetPatientSource(r.PatientSource)),
                                       new JProperty("IDCardNo", r.IDCardNo),
                                       new JProperty("PatientName", r.PatientName),
                                       new JProperty("Sex", r.Sex),
                                       new JProperty("Age", r.Age),
                                        new JProperty("Diagnosis", r.Diagnosis),
                                       new JProperty("Telephone", r.Telephone),
                                       new JProperty("Description", r.Description),
                                      // new JProperty("StatusTime", r.StatusTime),
                                       new JProperty("CreatedTime", r.CreatedTime.ToString("yyyy-MM-dd")) // new JProperty("Status", GetStatus(r.Status))
                               ))));

               return Ok(json.ToString());

      

        }
    }
}
