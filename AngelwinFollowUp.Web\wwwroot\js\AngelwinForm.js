﻿//AnyReport CRF表单保存数据和展示数据方法
window.addEventListener('message', function (event) {
    console.log(event);
    // 处理接收到的消息
    var res = event.data.data;
    var action = event.data.action;
    console.log(action);
    if (action == "show") {
        $("[name='save']").show();
    }
    if (res) {
        try {
            var obj = JSON.parse(res);

        } catch (error) {
            console.error('JSON字符串格式错误:', error.message);
            alert('JSON字符串格式错误:' + error.message);
        }

        obj.variables.forEach(function (variable) {
            var variableName = variable.variable_name;
            var value = variable.value;
            if (variableName.startsWith("detailArray_")) {
                const variableNameArray = variableName.split("_");
                var rows = variableNameArray[1];
                var match = rows.match(/\d+/);
                var XH = match ? parseInt(match[0], 10) : null;
                XH = XH + 1;
                var detailArray = JSON.parse(value);
                console.log('detailArray个数：', detailArray.length);
                //注册新增列
                for (var i = 0; i < detailArray.length; i++) {
                    var $rows = $('tr.' + rows + '[IsBindData!="1"]:not(:hidden)');
                    // 使用for...in循环遍历对象
                    for (var key in detailArray[i]) {

                        var thisValue = detailArray[i][key];
                        key = key.toUpperCase();
                        var controls = $rows.find('input[name^="' + key + '"]');
                        if (controls.length > 0) {
                            if (controls.is(':radio')) {
                                var radio = controls.filter('[value="' + thisValue + '"]');
                                radio.prop('checked', true);
                            }
                            else if (controls.is(':checkbox')) {
                                controls.prop("checked", false);
                                // 使用字符串的slice方法来截取字符串
                                if (thisValue) {
                                    var valueArr = thisValue.split("&&&###");
                                    valueArr.forEach(function (fruit) {
                                        var checkbox = controls.filter('[value="' + fruit + '"]');
                                        checkbox.prop('checked', true);
                                    });
                                }
                            }
                            else if (controls.is('select')) {
                                // var previousButton = control.parent('div').find('button');
                                if (thisValue) {
                                    var arrs = thisValue.split('&&&###');
                                    $(controls).selectpicker('val', arrs);
                                }
                            }
                            else {
                                controls.each(function () {
                                    $(this).prop('value', thisValue); // 设置每个input元素的value属性为value1
                                });
                            }
                        }
                    }
                    $rows.attr("IsBindData", "1");
                    if (i + 1 < detailArray.length) {
                        $('a.exticon[exttype="0"][cellref="A' + XH + '"]').click();
                    }
                }
            }
            else {
                variableName = variableName.toUpperCase();
                var control = $('[name="' + variableName + '"]');
                if (control.is(':radio')) {
                    var radio = $('[name="' + variableName + '"][value="' + value + '"]');
                    radio.prop('checked', true);

                }
                else if (control.is(':checkbox')) {

                    var CheckBoxList = $('input[name="' + variableName + '"][type="checkbox"]');
                    if (CheckBoxList.length > 0) {
                        CheckBoxList.prop("checked", false);
                        CheckBoxList.removeAttr("checked");
                        if (value) {
                            var valueArr = value.split("&&&###");
                            valueArr.forEach(function (fruit) {
                                var checkbox = $('[name="' + variableName + '"][value="' + fruit + '"]');
                                checkbox.prop('checked', true);
                            });
                        }
                    }
                }
                else if (control.is('select')) {
                    //var previousButton = control.parent('div').find('button');
                    if (value) {
                        var arrs = value.split('&&&###');
                        $(control).selectpicker('val', arrs);
                    }
                }
                else {
                    //20241107解决绘图 图片显示问题
                    if (variableName.indexOf("PICTURES_") > -1) {
                        var td = variableName.split("PICTURES_")[1];
                        var A9Td = anyrt.getFormCellByPos(td);
                        var htm = "<input type='hidden' value='" + value + "' name='" + variableName + "'><img class='img' src='" + value + "' style='height:126px;width:100px'>";
                        A9Td.html(htm);
                    }
                    else
                        control.val(value);
                }
            }

        });
    }

}, false)


//args:如果有动态行，则传动态行(class)的数组 没有则传[]
function saveData(args) {
    //获取查询按钮对象
    var btn1 = $("[name='SAVE']")
    //删除按钮默认的onclick事件
    btn1.attr("onclick", "");
    //注册查询按钮click事件
    btn1.click(function () {
        var form = document.getElementById('qryForm');
        var json = {};
        // 获取所有<tr>元素
        var trList = form.getElementsByTagName("tr");

        if (args.length > 0) {
            for (var i = 0; i < args.length; i++) {
                const rowsArray = Array.from(trList);
                var detailRows = rowsArray.filter(row => row.classList.contains(args[i]));
                var detailRowsArry = [];
                for (var j = 0; j < detailRows.length; j++) {
                    // 获取当前<tr>元素
                    var tr = detailRows[j];
                    var detailRowsData = {};
                    // 检查当前<tr>元素是否隐藏
                    var isHidden = tr.style.display === "none";

                    // 如果当前<tr>元素不隐藏，则获取其下所有input元素的值
                    if (!isHidden) {
                        // 获取当前<tr>元素下的所有<input>元素
                        var inputList = tr.getElementsByTagName("input");
                        // 遍历<input>元素列表
                        for (var k = 0; k < inputList.length; k++) {
                            // console.log(classs + "开始循环");
                            var input = inputList[k];
                            var key = input.name;
                            var value = input.value;
                            var type = input.type;
                            if (type == "button") {
                                // console.log("button", key);
                                continue;
                            }

                            if (type == "checkbox" || type == "radio") {
                                //33固定hash值32+分隔符
                                key = key.substring(0, key.length - 33);
                                if (!input.checked) {
                                    value = "";
                                }
                            }
                            if (key != 'formId') {
                                if (detailRowsData[key]) {
                                    if (value) {
                                        detailRowsData[key] = detailRowsData[key] + "&&&###" + value;
                                    }
                                } else {
                                    detailRowsData[key] = value;
                                }
                            }

                        }

                        var selectList = tr.getElementsByTagName("select");
                        // console.log("查找select", selectList.length);
                        for (var k = 0; k < selectList.length; k++) {
                            var input = selectList[k];
                            var key = input.name;
                            var selectedIndex = input.selectedIndex;
                            var selectedOption = "";
                            var types = input.type;
                            if (types == "select-multiple") {
                                selectedOption = $(input).selectpicker('val').join('&&&###');
                            }
                            else {
                                selectedOption = $(input).selectpicker('val')
                            }
                            detailRowsData[key] = selectedOption;
                        }

                        var textareaList = tr.getElementsByTagName("textarea");
                        // console.log("查找textarea", textareaList.length);
                        for (var k = 0; k < textareaList.length; k++) {
                            var input = textareaList[k];
                            var key = input.name;
                            var value = input.value;
                            detailRowsData[key] = value;
                        }
                        detailRowsArry.push(detailRowsData);
                    }
                }

                json["detailArray_" + args[i]] = detailRowsArry;

                //把动态行的从trList移除
                trList = rowsArray.filter(row => !row.classList.contains(args[i]));

            }
        }

        // 循环遍历每个<tr>元素
        for (var i = 0; i < trList.length; i++) {
            // 获取当前<tr>元素
            var tr = trList[i];

            // 检查当前<tr>元素是否隐藏
            var isHidden = tr.style.display === "none";

            // 如果当前<tr>元素不隐藏，则获取其下所有input元素的值
            if (!isHidden) {
                // 获取当前<tr>元素下的所有<input>元素
                var inputList = tr.getElementsByTagName("input");
                // 遍历<input>元素列表
                for (var j = 0; j < inputList.length; j++) {
                    // console.log(classs + "开始循环");
                    var input = inputList[j];
                    var key = input.name;
                    var value = input.value;
                    var type = input.type;
                    if (type == "button") {
                        // console.log("button", key);
                        continue;
                    }

                    if (type == "checkbox" || type == "radio") {
                        //33固定hash值32+分隔符
                        //   key = key.substring(0, key.length - 33);
                        if (!input.checked) {
                            value = "";
                        }
                    }
                    if (key != 'formId') {
                        if (json[key]) {
                            if (value) {
                                json[key] = json[key] + "&&&###" + value;
                            }
                        } else {

                            json[key] = value;
                        }
                    }

                }

                var selectList = tr.getElementsByTagName("select");
                // console.log("查找select", selectList.length);
                for (var j = 0; j < selectList.length; j++) {
                    var input = selectList[j];
                    var key = input.name;
                    var selectedIndex = input.selectedIndex;
                    var selectedOption = "";
                    var types = input.type;
                    if (types == "select-multiple") {
                        selectedOption = $(input).selectpicker('val').join('&&&###');
                    }
                    else {
                        selectedOption = $(input).selectpicker('val')
                    }
                    json[key] = selectedOption;
                }

                var textareaList = tr.getElementsByTagName("textarea");
                // console.log("查找textarea", textareaList.length);
                for (var j = 0; j < textareaList.length; j++) {
                    var input = textareaList[j];
                    var key = input.name;
                    var value = input.value;
                    json[key] = value;
                }
            }

        }
        // 检查anyrt对象是否存在并且有formValid方法
        var res = true; // 默认为true
        if (typeof anyrt !== 'undefined' && typeof anyrt.formValid === 'function') {
            console.log('anyrt对象存在，开始表单验证');
            console.log('anyrt对象:', anyrt);

            // 尝试获取表单验证详细信息
            try {
                res = anyrt.formValid();
                console.log('anyrt.formValid() result:', res);

                // 如果验证失败，尝试获取更多信息
                if (!res) {
                    console.log('表单验证失败，尝试获取详细信息');

                    // 检查是否有验证错误信息
                    if (typeof anyrt.getValidationErrors === 'function') {
                        var errors = anyrt.getValidationErrors();
                        console.log('验证错误:', errors);
                    }

                    // 检查表单中的必填字段
                    var form = document.getElementById('qryForm');
                    if (form) {
                        var requiredFields = form.querySelectorAll('[required], [data-required="true"], [lay-verify*="required"]');
                        console.log('必填字段数量:', requiredFields.length);

                        requiredFields.forEach(function(field, index) {
                            var value = field.value || '';
                            console.log('必填字段 ' + (index + 1) + ':', {
                                name: field.name || field.id,
                                value: value,
                                isEmpty: value.trim() === '',
                                type: field.type,
                                tagName: field.tagName
                            });
                        });
                    }
                }
            } catch (error) {
                console.error('调用anyrt.formValid()时发生错误:', error);
                res = true; // 如果验证出错，默认通过
            }
        } else {
            console.warn('anyrt对象或formValid方法不存在，跳过表单验证');
            console.log('window.anyrt:', typeof window.anyrt);
            console.log('全局对象检查:', Object.keys(window).filter(key => key.toLowerCase().includes('anyrt')));
        }

        var jsonString = JSON.stringify(json);
        console.log('表单数据:', jsonString);
        console.log('验证结果:', res);

        // 向父窗口发送消息
        if (res) {
            console.log('发送保存消息到父窗口');
            window.parent.postMessage({ action: 'save', url: this.href, data: jsonString }, '*');
        } else {
            console.log('表单验证失败，询问用户是否强制保存');

            // 询问用户是否要强制保存
            if (confirm('表单验证失败，是否要强制保存？\n\n点击"确定"强制保存，点击"取消"返回修改表单。')) {
                console.log('用户选择强制保存');
                window.parent.postMessage({ action: 'save', url: this.href, data: jsonString }, '*');
            } else {
                console.log('用户取消保存');
            }
        }
    })
};


$('input').click(function () {
    var $name = $(this).attr('name');
    window.parent.postMessage({ action: 'show', url: this.href, fieldname: $name }, '*');
});

$('textarea').click(function () {
    var $name = $(this).attr('name');
    window.parent.postMessage({ action: 'show', url: this.href, fieldname: $name }, '*');
});

$('select').click(function () {
    var $name = $(this).attr('name');
    window.parent.postMessage({ action: 'show', url: this.href, fieldname: $name }, '*');
});