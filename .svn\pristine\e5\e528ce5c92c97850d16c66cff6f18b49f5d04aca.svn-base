﻿@{
    ViewBag.Title = "人员管理";
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>人员管理</title>
    <link href="~/layuiadmin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/layuiadmin/style/admin.css" rel="stylesheet" />
    <script src="~/layuiadmin/layui/layui.js"></script>
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/js/common.js"></script>
    <style>

        .layui-tab-brief {
            background-color: #fff;
        }
        .search_wrap {
            background-color: #f0f0f0;
        }

        .line_wrap {
            padding: 10px 15px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .layui-colla-title {
            padding: 0 15px 0 15px;
        }

        .layui-show {
            display: flex !important;
            align-items: flex-end;
        }

        .form_wrap {
            flex: 1;
        }

        .form_item {
            padding-top: 10px;
        }

        .btnwrap {
            padding-left: 10px;
        }

        .table_wrap {
            overflow: hidden;
        }

        .layui-form-label {
           padding:8px 6px 8px 6px;
        }

/*        .layui-form-label {
            width: 120px;
        }

        .layui-input-block {
            margin-left: 150px;
        }*/
        /* 定义表头样式 */
        .layui-table-header .layui-table-cell {
            font-weight: bold; /* 加粗 */
            font-size: 14px; /* 可选：调整字体大小以提升可读性 */
            color: #333; /* 可选：调整字体颜色 */
        }

        /*弹窗*/
        .window_wrap {
            padding: 15px;
        }

        userList_window .layui-form-label {
            width: 100px;
        }

        userList_window .layui-form-val {
            padding: 9px 15px;
        }

        userList_window .layui-form-item {
            margin-bottom: 0;
        }

 /*       .min_width {
            width: 300px !important;
        }*/

        .min_top {
            margin-top: 8px;
        }

        .ue-container {
            width: 100%;
            margin: 0 auto;
            margin-top: 3%;
            background: #fff;
        }

        .flex_center {
            display: flex;
            flex-direction: row;
            justify-content: center;
        }

        .flex_between {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .tools_l {
            flex: 1;
        }
    </style>
</head>


<body style="padding:5px;">
    <div class="container layui-form" style="display:none">
        <div class="layui-tab-brief">
            <ul class="layui-tab-title">
                <li class="layui-this" data-value="01">用户管理</li>
           @*      <li data-value="02">医疗组</li>
                <li data-value="03">科室</li> *@
            </ul>
        </div>
  

      
    </div>





    <div class="layui-col-md12">

        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-inline">
                    <label class="layui-form-label">关键字:</label>
                    <div class="layui-input-inline" style="width:300px;">
                        <input type="text" class="layui-input" name="KeyWords" id="KeyWords" />
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-input-block">
                        <button class="layui-btn fr layui-btn-primary layui-border-green" id="Search"><i class="layui-icon layui-icon-search"></i></button>
                        <button class="layui-btn layui-btn layui-bg-blue" id="addUser" data-type="add"><i class="layui-icon layui-icon-addition"></i></button>
                        <button class="layui-btn layui-btn layui-bg-blue" id="importUser" data-type="add">批量导入</button>
                        @*<button type="button" class="layui-btn" id="imageUpload">
                            <i class="layui-icon">&#xe67c;</i>上传图片
                        </button>*@
                    </div>
                </div>

            </div>

            <div class="layui-card-body">
                <script type="text/html" id="toolbarDemo">
                    <div class="layui-btn-container">
                        <button class="layui-btn layui-btn" lay-event="Role">角色管理</button>
                        @*  <button class="layui-btn layui-btn" lay-event="Purview">权限管理</button>  *@
                    </div>
                </script>
                <table class="layui-hide" id="tablelist" lay-filter="tablelist"></table>
                <script type="text/html" id="tableBar1">
                    <a class="layui-btn layui-btn-xs" lay-event="key"><i class="layui-icon layui-icon-password"></i>PSK</a>
                    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
                    @*<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>*@
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="reset"><i class="layui-icon layui-icon-password"></i>重置密码</a>
                    @*<a class="layui-btn layui-btn layui-btn-xs" lay-event="addrole"><i class="layui-icon layui-icon-set-fill"></i>角色管理</a>*@
                </script>
            </div>
        </div>



        <script src="~/layuiadmin/layuiextend/xm-select.js"></script>
        <script>
            layui.config({
                base: '/layuiadmin/layuiextend/'
            }).use(['element', 'layer', 'laydate', 'upload', 'table', 'form','transfer'], function () {
                var element = layui.element
                    , layer = layui.layer
                    , table = layui.table//表格
                    , laydate = layui.laydate
                    , $ = layui.$
                    , upload = layui.upload
                    , transfer = layui.transfer
                    , form = layui.form ;

                 var titles = '@ViewBag.titles';
                var windowsIndex;
                var selectbox;
                var url = '';
                var selectTableRowData = [];
                var LockoutEnd = '';
                var userid = '';
                var Lockstatus = '';
                var Uid = '';
                var value = '01';

                // var xmDeptsList = xmSelect.render({
                //     el: '#xmDeptsList',
                //     model: { label: { type: 'text' } },
                //     prop: {
                //         name: 'title',
                //         value: 'id',
                //     },
                //     minWidth: 200,
                //     radio: true,
                //     filterable: true,
                //     clickClose: true,
                //     //树
                //     tree: {
                //         show: true,
                //         //非严格模式
                //         strict: false,
                //         //默认展开节点
                //         expandedKeys: [-1],
                //     },
                //     data: [],
                //     // 添加 onChange 事件处理器
                //     on: function (val) {
                //         GetGroupsTree(val.arr[0].id);
                //     }
                // });

                var xmGroupList = xmSelect.render({
                    el: '#xmGroupList',
                    model: { label: { type: 'text' } },
                    prop: {
                        name: 'title',
                        value: 'id',
                    },
                    minWidth: 200,
                    radio: false,
                    filterable: true,
                    clickClose: true,
                    //树
                    tree: {
                        show: true,
                        //非严格模式
                        strict: false,
                        //默认展开节点
                        expandedKeys: [-1],
                    },
                    data: []
                });

                // var xmCentersList = xmSelect.render({
                //     el: '#xmCenterNew',
                //     model: { label: { type: 'text' } },
                //     prop: {
                //         name: 'title',
                //         value: 'id',
                //     },
                //     minWidth: 200,
                //     radio: true,
                //     filterable: true,
                //     clickClose: true,
                //     //树
                //     tree: {
                //         show: true,
                //         //非严格模式
                //         strict: false,
                //         //默认展开节点
                //         expandedKeys: [-1],
                //     },
                //     data: []
                // });

                function GetPurviewDeptsTree() {
                    $.ajax({
                        url: '/User/GetHospitalDeptTreeList',
                        type: "post",
                        datatype: 'json',
                        success: function (result) {
                            xmDeptsList.update({
                                data: result
                            });
                            if (result[0].id) {
                                var arr = new Array();
                                arr.push(result[0].id);
                                xmDeptsList.setValue(arr);
                                GetGroupsTree(result[0].id);
                            }

                        }, error: function () {
                            layer.msg("获取失败！");
                        }
                    })
                };

                function GetGroupsTree(val) {
                    $.ajax({
                        url: '/CommAPI/GetGroupTreeList?hospitalDeptId=' + val,
                        type: "post",
                        datatype: 'json',
                        success: function (result) {
                            xmGroupList.update({
                                data: result
                            });
                        }, error: function () {
                            layer.msg("获取失败！");
                        }
                    })
                };

                function GetCentersTree(value) {
                    $.ajax({
                        url: '/CommAPI/GetCentersList',
                        type: "post",
                        datatype: 'json',
                        data: { 'Type': value },
                        success: function (result) {
                            xmCentersList.update({
                                data: result
                            });
                            if (result[0].id) {
                                var arr = new Array();
                                arr.push(result[0].id);
                                setTimeout(function () {
                                }, 1000);

                            }

                        }, error: function () {
                            layer.msg("获取失败！");
                        }
                    })
                };

                // 监听开关状态操作
                form.on('switch(mySwitch)', function (data) {
                    var controlElement = document.getElementById('Glist');
                    // 当前开关是否开启，true 或者 false
                    var checked = data.elem.checked;
                    // 在这里可以执行你的逻辑，例如发送 AJAX 请求更新数据库中的状态
                    if (checked) {
                        console.log('开关已开启');
                        controlElement.style.display = 'inline-block'; // 显示控件
                    } else {
                        console.log('开关已关闭');
                        controlElement.style.display = 'none'; // 隐藏控件
                    }
                });
                table.render({
                    elem: '#Purviewlist'
                    , id: 'Purviewlist'
                    , cols: [[
                        { field: 'zizeng', title: '', type: 'numbers' }
                        , { field: 'hospitalDepts', title: '医学中心', width: 180 }
                        , { field: 'limitzbz', title: '限制专病组', width: 120 }
                        , { field: 'zbz', title: '专病组' }
                        , { fixed: 'right', align: 'center', title: '操作', width: 150, toolbar: '#tablePurviewBar' }
                    ]]
                    , page: false // 关闭分页
                });


                //上传

                var UploadIndes;
                upload.render({
                    elem: '#addFileBtn'
                    , url: '/User/UploadFile' //改成您自己的上传接口
                    , multiple: false
                    , accept: 'file' //普通文件
                    , acceptMime: 'application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    , exts: 'xls|xlsx'
                    , before: function (obj) {

                        UploadIndes = layer.load(1);
                        $("#UploadSpan").css("display", "");
                    }
                    , done: function (res) {


                        tableRender([]);
                        if (res.status) {
                            tableRender(res.data);

                        }
                        else {
                            layer.msg(res.msg);
                        }

                        $("#UploadSpan").css("display", "none");
                        layer.close(UploadIndes);
                    }
                });
                laydate.render({
                    type: 'datetime'
                    , elem: '#LockoutEnd' //指定元素
                    , min: 1
                    , max: 15
                    , format: 'yyyy-MM-dd HH:mm:ss'
                    , done: function (value, date, endDate) {
                        LockoutEnd = value;
                    }
                });
                laydate.render({
                    type: 'datetime'
                    , elem: '#RLockoutEnd' //指定元素
                    , min: 1
                    , max: 15
                    , format: 'yyyy-MM-dd HH:mm:ss'
                    , done: function (value, date, endDate) {
                        LockoutEnd = value;
                    }
                });
                //tablelist
                table.render({
                    elem: '#tablelist'
                    , id: 'tablelist'
                    , page: true
                    , limit: 20
                    , height: 'full-105'
                    , toolbar: '#toolbarDemo'
                    , cols: [[
                        { type: 'radio' }
                        , { field: 'zizeng', title: '', type: 'numbers' }
                        , { field: 'UserName', title: '用户名', width: 110 }
                        , { field: 'TrueName', title: '真实姓名' }
                        // , { field: 'CenterName', title: '所属中心', width: 150 }
                        // , { field: 'HospitalDeptName', title: '科研机构', width: 150 }
                        , { field: 'EmployeeNum', title: '工号' }
                        , { field: 'PhoneNumber', title: '手机号码' }
                        , { field: 'Email', title: '电子邮箱' }
                         , { field: 'DDUserId', title: titles+ '号' }
                        , { field: 'id', title: 'ID', hide: 'true' }
                        , { field: 'StopUsing', title: '停用', width: 95, templet: '#switchTpl', unresize: true }
                        , { field: 'IsLockedOut', title: '锁定', width: 110, templet: '#checkboxTpl', unresize: true }
                        , { field: 'LockoutEnd', title: '锁定到', minWidth: 100 }
                        , { field: 'LastUpDatePwdTime', title: '修改密码时间', minWidth: 120 }
                        , { field: 'Roles', title: '拥有角色' }
                        , { fixed: 'right', align: 'center', title: '操作', width: 265, toolbar: '#tableBar1' }
                    ]]
                    , done: function (res, curr, count) {
                        $('.layui-table-view[lay-id="tablelist"]').children('.layui-table-box')
                            .children('.layui-table-body').find('table tbody tr[data-index=0]').click();
                    }
                });

                $(document).ready(function () {
                    $('.layui-tab-title li').on('click', function () {
                        // 获取被点击列表项的 value
                        value = $(this).data('value');

                        var labelText = $(this).text();
                        $('#labelContent').text(labelText);
                      //  GetDeptsTree(value);
                        EmptyData();
                        SearchData();
                    })

                    $(document).on('click', '#addUser', function () {
                        $("#fm")[0].reset();
                        $('#Id').val(0);
                         //xmDept.setValue([]);
                        windowsIndex = layer.open({
                            type: 1,
                            title: '新增用户',
                            area: ['50%', '90%'],
                            resize: true,
                            content: $('#form_window'),
                            btn: ['提交', '重置','取消'],
                            yes: function (index, layero) {
                                var submit = $("#form_window").find("#fm").find("#submit");
                                submit.click();

                            },
                             btn2: function (index, layro) {

                                 var reset = $("#form_window").find("#fm").find("#btn_reset");
                                 reset.click();

                                 return false;
                            },
                            btn3: function (index, layro) {
                                var indes = layer.load(1);
                                layer.close(indes);
                            }
                        });
                        url = '/User/Register';
                    });
                  //  GetDeptsTree(value);
                   // GetCentersTree(value);
                    $(document).on('click', '#Search', function () {
                        EmptyData();
                        SearchData();
                    });

                    $(document).on('click', '#download', function () {
                        //下载Excel模板
                        downloadExcel();
                    });
                    $(document).on('click', '#addUser', function () {
                        EmptyData();
                    });

                    $(document).on('click', '#importUser', function () {

                        tableRender([]);
                        windowsIndex = layer.open({
                            title: '批量导入用户',
                            area: ['65%', '60%'],
                            type: 1,
                            resize: true,
                            content: $('#form_window_Import')
                        });
                    });

                    $(document).on('click', '#ckuser', function () {
                        checkUser();
                    });

                    $(document).on('click', '#ckemail', function () {
                        checkEmail();
                    });
                    $(document).on('click', '#ckUserId', function () {
                        ckUserId();
                    });

                    $(document).on('click', '#ckding', function () {
                        ding();
                    });

                });

                //监听tablelist行单击事件(双击事件为：rowDouble)
                table.on('row(tablelist)', function (obj) {
                    var data = obj.data;
                    selectTableRowData = [];
                    selectTableRowData = data;
                    obj.tr.addClass('layui-table-click').siblings().removeClass('layui-table-click');
                    obj.tr.find('i[class="layui-anim layui-icon"]').trigger("click");
                    if (data.HospitalDeptId) {
                        var arr = new Array();
                        arr.push(data.HospitalDeptId);
                       // xmDeptsListNew.setValue(arr);
                    } else {
                       // xmDeptsListNew.setValue([]);
                    }
                    if (data.MultiCenterId) {
                        var arr = new Array();
                        arr.push(data.MultiCenterId);
                     //   xmCentersList.setValue(arr);
                    } else {
                       // xmCentersList.setValue([]);
                    }
                    if (data.StopUsing == 'on') {
                        data.StopUsing = true;
                    }
                    else {
                        data.StopUsing = false;
                    }
                    if (data.IsLockedOut == 'on') {
                        data.IsLockedOut = true;
                    }
                    else {
                        data.IsLockedOut = false;
                    }
                    form.val('filterInfo', data);

                    form.render('checkbox', 'filterInfo');

                });

                //监听停用操作
                form.on('switch(sexDemo)', function (obj) {
                    console.log(this.name);
                    console.log(obj);
                    layer.confirm('确定要变更状态吗', {
                        title: '',
                        btn: ['确定', '取消'], //按钮
                        cancel: function (index, layero) {
                            relodcheck(obj);
                        },
                        resize: false
                    }, function (index) {
                        $.ajax(
                            {
                                url: '/User/Updatestatus',
                                type: "post",
                                data: { 'id': obj.elem.name, 'status': obj.elem.checked, 'locktpye': 'StopUsing', 'LockoutEnd': LockoutEnd },
                                datatype: 'json',
                                success: function (data) {
                                    debugger
                                    if (data.okMsg) {
                                        layer.msg(data.okMsg);
                                        layer.close(windowsIndex);//关闭弹出层
                                        table.reload('tablelist'); //重载表格
                                    }

                                }
                            }
                        )
                    }
                        , function (index) {
                            debugger;
                            var checkdstatus = "";
                            relodcheck(obj);
                        }
                    );

                });

                function relodcheck(obj, type) {

                    $.ajax(
                        {
                            url: '/User/UserList',
                            type: "post",
                            data: { 'userid': obj.elem.name, 'limit': "10", 'page': "1" },
                            datatype: 'json',
                            success: function (data) {
                               // console.log(data.data);
                               // console.log(data.data[0]);
                              //  console.log(data.data[0].StopUsing);
                                if (type == "lockDemo") {
                                    type = data.data[0].IsLockedOut
                                }
                                else {
                                    type = data.data[0].StopUsing
                                }

                                if (type == "off") {
                                    obj.elem.checked = false;
                                    obj.elem.value = "on";

                                    form.render();

                                } else {
                                    obj.elem.checked = true;
                                    obj.elem.value = "off";
                                    form.render();
                                    console.log(obj);
                                }
                            }
                        }
                    )


                }
                //监听锁定操作
                form.on('checkbox(lockDemo)', function (obj) {

                    console.log(obj.elem.checked);
                    Lockstatus = obj.elem.checked;
                    userid = obj.elem.name;
                    if (obj.elem.checked == "true" || obj.elem.checked == true) {
                        debugger;
                        console.log(obj.elem.checked);
                        console.log("1");

                        layer.open(
                            {
                                type: 1
                                , title: '锁定'
                                , area: ['500px', '100px']
                                , content: $('#Role_Lock_window')
                                , cancel: function (index, layero) {
                                    relodcheck(obj, "lockDemo")
                                }
                            }
                        )


                    }
                    else {
                        layer.confirm('确定要变更状态吗', {
                            title: '',
                            btn: ['确定', '取消'], //按钮
                            cancel: function (index, layero) {
                                relodcheck(obj, "lockDemo")
                            },
                            resize: false
                        }, function (index) {
                            $.ajax(
                                {
                                    url: '/User/Updatestatus',
                                    type: "post",
                                    data: { 'id': obj.elem.name, 'status': obj.elem.checked, 'locktpye': 'IsLockedOut', 'LockoutEnd': LockoutEnd },
                                    datatype: 'json',
                                    success: function (data) {
                                        debugger;
                                        if (data.okMsg) {
                                            layer.msg(data.okMsg);
                                            layer.close(windowsIndex);//关闭弹出层
                                            table.reload('tablelist'); //重载表格
                                        }
                                    }
                                }
                            )

                        }
                            , function (index) {
                                relodcheck(obj, "lockDemo")
                            }

                        )
                    }
                });

                table.on('tool(Purviewlist)', function (obj) {
                    if (obj.event === 'PurviewDel') {
                        $.ajax({
                            url: '/User/DeletePurview',
                            type: "post",
                            data: { 'userid': Uid, 'Depid': obj.data.hospitalDeptId },
                            datatype: 'json',
                            success: function (data) {
                                if (data.okMsg) {
                                    table.reload('Purviewlist', {
                                        page: {
                                            curr: 1
                                        },
                                        url: '/User/PurviewLis'
                                        , where: {
                                            'userid': Uid
                                        }
                                    });
                                }
                                else {
                                    layer.msg(data.errorMsg);
                                }
                            }, error: function (res) {
                                layer.msg("删除权限出错：" + res.responseText);
                            }
                        });
                    }
                });

                //监听tablelist工具条
                table.on('tool(tablelist)', function (obj) {
                    var data = obj.data;
                    if (obj.event === 'edit') {
                        debugger
                        $('#UserName_up').attr("readonly", "readonly");
                        $('#Email_up').attr("readonly", "readonly");

                        if (data.StopUsing == 'on') {
                            data.StopUsing = true;
                        }
                        else {
                            data.StopUsing = false;
                        }
                        if (data.IsLockedOut == 'on') {
                            data.IsLockedOut = true;
                        }
                        else {
                            data.IsLockedOut = false;
                        }
                        form.val('fmupdate', data);
                        if (data.HospitalDeptId != undefined) {
                            xmDeptsListUpdate.setValue([data.HospitalDeptId]);
                        }
                        form.render('checkbox', 'fmupdate'); //更新 lay-filter="fmupdate" 所在容器内的全部 checkbox 状态
                        windowsIndex = layer.open({
                            type: 1,
                            title: '修改【' + data.UserName + '】用户',
                            area: '600px',
                            resize: true,
                            content: $('#updateform_window')
                        });
                    }
                    else if (obj.event === 'del') {
                        layer.confirm('确定要删除名为【' + data.UserName + '】的用户以及该用户的所有配置吗？将无法恢复。', {
                            title: '',
                            btn: ['确定', '取消'], //按钮
                            resize: false
                        }, function (index) {
                            $.post('/User/DelUser', { id: data.Id }, function (result) {
                                if (result.okMsg) {
                                    layer.msg(result.okMsg);
                                    currentIndex = -1;
                                    table.reload('tablelist'); //重载表格
                                } else {
                                    layer.msg(result.errorMsg);
                                }
                            }, 'json');
                            layer.close(index);
                        });
                    }
                    else if (obj.event === 'reset') {
                        layer.confirm('确定要重置名为【' + data.UserName + '】的用户密码么（重置后登录密码和用户名相同）？', {
                            title: '',
                            btn: ['确定', '取消'], //按钮
                            resize: false
                        }, function (index) {
                            $.post('/User/ResetPassword', { id: data.Id }, function (result) {
                                if (result.okMsg) {
                                    layer.msg(result.okMsg);
                                } else {
                                    layer.msg(result.errorMsg);
                                }
                            }, 'json');
                            layer.close(index);
                        });
                    }
                    else if (obj.event === 'Role') {
                        windowsIndex = layer.open({
                            type: 1,
                            title: '设置【' + data.UserName + '】角色',
                            area: ['800px', '600px'],
                            resize: true,
                            btn: ['保存', '取消'],
                            yes: function (index, layero) {
                                 var getData = transfer.getData('key123').map(function (obj, index) {
                                                return obj.value;
                                            }).join(","); //获取右侧数据
                                console.log(getData);
                                $.post('/User/UsersOfRole', { userid: data.Id, roles: getData }, function (result) {
                                    table.reload('tablelist'); //重载表格
                                }, 'json');
                                layer.close(index);

                            },
                            cancel: function (index, layro) {
                               // $('.demo').empty();
                            },
                            content: $('#Role_window')
                        });
                        LoadDoublebox(data.Roles, data.Id);

                    }
                    else if(obj.event=== 'key')
                    {
                         $("#txtUrl").val("");
                         $("#txtPSK").val("");
                         $("#imgQRCode").attr("src","");
                        $("#txtUserName").val(data.UserName);

                        windowsIndex = layer.open({
                            type: 1,
                            title: '设置【' + data.UserName + '】的PSK',
                            area: ['700px', '500px'],
                            resize: true,
                          //  btn: ['保存', '取消'],
                            yes: function (index, layero) {

                                $.post('/User/submitPSK', { UserName: data.UserName, Key: $("#txtPSK").val() }, function (res) {
                                   layer.msg(res.data.msg);
                                }, 'json');
                                layer.close(index);

                            },
                            cancel: function (index, layro) {
                                // $('.demo').empty();
                            },
                            content: $('#window_psk'),
                            success: function (layro) {
                               $.get('/User/getPSK?UserName='+data.UserName,function(res){

                                 if(res.code==0)
                                 {
                                    $("#txtUrl").val(res.data.url);
                                    $("#txtPSK").val(res.data.key);
                                    $("#imgQRCode").attr("src","data:image/png;base64,"+res.data.qrCode);
                                 }
                               })
                            }
                        });
                    }

                });

                table.on('toolbar(tablelist)', function (obj) {
                    var data = table.checkStatus('tablelist').data[0];
                    Uid = data.Id;
                    if (obj.event === 'Purview') {
                        GetPurviewDeptsTree();
                        table.reload('Purviewlist', {
                            page: {
                                curr: 1
                            },
                            url: '/User/PurviewLis'
                            , where: {
                                'userid': data.Id
                            }
                        });
                        windowsIndex = layer.open({
                            type: 1,
                            title: '设置【' + data.UserName + '】权限',
                            area: ['1000px', '600px'],
                            resize: true,
                            cancel: function (index, layro) {
                                layer.close(index);
                            },
                            content: $('#Purview_window')
                        });
                    }

                    else if (obj.event === 'Role') {
                        windowsIndex = layer.open({
                            type: 1,
                            title: '设置【' + data.UserName + '】角色',
                            area: ['800px', '600px'],
                            resize: true,
                            btn: ['保存', '取消'],
                            yes: function (index, layero) {
                                var test = transfer.getData('key123').map(function (obj, index) {
                                    return obj.value;
                                }).join(","); //获取右侧数据
                                console.log(test);
                                $.post('/User/UsersOfRole', { userid: data.Id, roles: test }, function (result) {
                                    table.reload('tablelist'); //重载表格
                                }, 'json');
                                layer.close(index);

                            },
                            cancel: function (index, layro) {
                                $('.demo').empty();
                            },
                            content: $('#Role_window')
                        });
                        LoadDoublebox(data.Roles, data.Id);

                    }
                });

                // 监听“添加”按钮的点击事件
                $("#AddPurview").click(function () {
                    // 获取选择的数据
                    var DepId = xmDeptsList.getValue('valueStr');
                    var zbzId = xmGroupList.getValue('valueStr');
                    $.ajax({
                        url: '/User/UpdatePurview',
                        type: "post",
                        data: { 'userid': Uid, 'Depid': DepId, 'zdzid': zbzId },
                        datatype: 'json',
                        success: function (data) {
                            debugger
                            if (data.okMsg) {
                                table.reload('Purviewlist', {
                                    page: {
                                        curr: 1
                                    },
                                    url: '/User/PurviewLis'
                                    , where: {
                                        'userid': Uid
                                    }
                                });
                            }
                            else {
                                layer.msg(data.errorMsg);
                            }
                            layer.close(indes);
                        }, error: function (res) {
                            layer.msg("添加权限出错：" + res.responseText);
                            layer.close(indes);
                        }
                    });
                });

                //监听提交
                form.on('submit(submit)', function (data) {
                    var indes = layer.load(1);
                    //提交 Ajax 成功后，关闭当前弹层并重载表格
                 //   data.field.HospitalDeptId = xmDeptsListNew.getValue('valueStr');
                   data.field.Id = $("#Id").val();
                  //  data.field.MultiCenterId= xmCentersList.getValue('valueStr');
                    $.ajax({
                        url: '/User/Register',
                        type: "post",
                        data: { 'model': data.field },
                        datatype: 'json',
                        success: function (data) {
                            debugger
                            if (data.okMsg) {
                                layer.msg(data.okMsg);
                                layer.close(windowsIndex);//关闭弹出层
                                table.reload('tablelist'); //重载表格

                            }
                            else {
                                layer.msg(data.errorMsg);
                            }
                            layer.close(indes);
                        }, error: function (res) {
                            layer.msg("加载统计信息错误：" + res.responseText);
                            layer.close(indes);
                        }
                    });
                    return false;
                });

                form.on('submit(submitupdate)', function (data) {
                    var indes = layer.load(1);
                    //提交 Ajax 成功后，关闭当前弹层并重载表格
                   // data.field.HospitalDeptId = xmDeptsListUpdate.getValue('valueStr');
                    $.ajax({
                        url: '/User/EditUser',
                        type: "post",
                        data: { 'model': data.field },
                        datatype: 'json',
                        success: function (data) {
                            debugger
                            if (data.okMsg) {
                                layer.msg(data.okMsg);
                                layer.close(windowsIndex);//关闭弹出层
                                table.reload('tablelist'); //重载表格
                            }
                            else {
                                layer.msg(data.errorMsg);
                            }
                            layer.close(indes);
                        }, error: function (res) {
                            layer.msg("加载统计信息错误：" + res.responseText);
                            layer.close(indes);
                        }
                    });
                });

                $(document).on('click', '#LockStatus', function () {
                    if (LockoutEnd == null || LockoutEnd == '') {
                        layer.alert("请选择锁定日期！", { icon: 5 });
                        return;
                    }
                    layer.confirm('确定要锁定吗?', {
                        title: '',
                        btn: ['确定', '取消'], //按钮
                        resize: false

                    }, function (index) {
                        $.ajax(
                            {
                                url: '/User/Updatestatus',
                                type: "post",
                                data: { 'id': userid, 'status': Lockstatus, 'locktpye': 'IsLockedOut', 'LockoutEnd': LockoutEnd },
                                datatype: 'json',
                                success: function (data) {
                                    debugger
                                    LockoutEnd = '';
                                    if (data.okMsg) {
                                        layer.msg(data.okMsg);
                                        layer.closeAll();//关闭所有弹出层
                                        table.reload('tablelist'); //重载表格
                                    }
                                }
                            }
                        )
                    })
                });

                form.verify({
                    usernameck: [
                        /^[a-zA-Z][a-zA-Z0-9_]{3,14}$/
                        , "用户名不合法（字母开头，允许4-15字节，允许字母数字下划线）"
                    ],
                    emailck: [
                        /^\w+([-+.]\w+)*@@\w+([-.]\w+)*\.\w+([-.]\w+)*$/
                        , '请输入有效的电子邮件账号(例：abc@@126.com)'
                    ],
                    mobileck: [
                        /^(086|\+86|17951)?(13|14|15|17|18)\d{9}$/
                        , '手机号码格式不正确。'
                    ]
                });
                function EmptyData() {
                    $('#Id').val('');
                    $('#EmployeeNum').val('');
                    $('#TrueName').val('');
                    $('#PhoneNumber').val('');
                    $('#HospitalDeptName').val('');
                    $('#UserName').val('');
                    $('#Email').val('');
                    $('#DDUserId').val('');
                    $('#LockoutEnd').val('');
                    $('#LockoutEnd').val('');
                    $('#StopUsing').attr('checked', false);
                    $('#IsLockedOut').attr('checked', false);

                    // xmDeptsListNew.update({
                    //     disabled: false
                    // });
                    // xmCentersList.update({
                    //     disabled: false
                    // });
                    // xmDeptsListNew.setValue([]);
                    // xmCentersList.setValue([]);
                    form.render();
                }
                function SearchData() {
                    table.reload('tablelist', {
                        page: {
                            curr: 1
                        },
                        url: '/User/UserList'
                        , where: {
                             'keyWord': $.trim($("#KeyWords").val()),
                            'deptType': value
                        }
                    });
                };


                function transferRender(data,value) {
                    //显示搜索框
                    transfer.render({
                        elem: '#test4'
                        , data: data
                        , width: 300
                        , height: 450
                        , title: ['选择角色', '已选角色']
                        , showSearch: true
                        , value: value
                        , id: 'key123'
                    });
                    //form.render();
                }

                function LoadDoublebox(Roles, Userid) {
                    $.ajax({
                        url: '/User/RoleList' ,
                        type: "post",
                        success: function (data) {


                            if (Roles != "") {
                                $.ajax(
                                    {
                                        url: '/User/UserRoleList' + '?userid=' + Userid,
                                        type: "post",
                                        success: function (data2) {

                                            transferRender(data, JSON.stringify(data2));
                                        }

                                    }
                                )
                            } else {
                                transferRender(data,"");
                            }
                        }
                    })



                }


                function checkUser() {
                    var UserName = $.trim($("#UserName").val());
                    if (UserName == '') {
                        layer.msg("用户名不能为空");
                        return false;
                    }

                    $.ajax({
                        url: '/User/CheckUserName',
                        type: "post",
                        data: { 'UserName': UserName },
                        datatype: 'json',
                        success: function (data) {
                            if (data.okMsg) {
                                layer.msg(data.okMsg);
                            }
                            else {
                                layer.msg(data.errorMsg);
                            }
                        }, error: function (res) {
                            layer.msg("加载统计信息错误：" + res.responseText);
                        }
                    });
                }
                function ckUserId() {
                    var DDUserId = $.trim($("#DDUserId").val());
                    if (DDUserId == '') {
                        layer.msg("用户名不能为空");
                        return false;
                    }

                    $.ajax({
                        url: '/User/CheckDDUserId',
                        type: "post",
                        data: { 'DDUserId': DDUserId },
                        datatype: 'json',
                        success: function (data) {
                            if (data.okMsg) {
                                layer.msg(data.okMsg);
                            }
                            else {
                                layer.msg(data.errorMsg);
                            }
                        }, error: function (res) {
                            layer.msg("加载统计信息错误：" + res.responseText);
                        }
                    });
                }

                function checkEmail() {
                    var Email = $.trim($("#Email").val());
                    if (Email == '') {
                        layer.msg("电子邮箱不能为空!");
                        return false;
                    }

                    $.ajax({
                        url: '/User/CheckEmail',
                        type: "post",
                        data: { 'Email': Email },
                        datatype: 'json',
                        success: function (data) {
                            if (data.okMsg) {
                                layer.msg(data.okMsg);
                            }
                            else {
                                layer.msg(data.errorMsg);
                            }
                        }, error: function (res) {
                            layer.msg("加载统计信息错误：" + res.responseText);
                        }
                    });
                }

                function ding() {
                    var phone = $('#PhoneNumber').val();
                    if (!phone) {

                        layer.msg('未填写手机号码！');
                        return;
                    }
                    var indes = layer.load(1);
                    $('#DDUserId').val('');
                    $.ajax(
                        {
                            url: '/DingTalk/GetUserIdByPhone',
                            type: "post",
                            data: { 'phone': phone },
                            datatype: 'json',
                            success: function (data) {

                                var result = eval("(" + data + ")")
                                //console.log(result);
                                if (result.status) {
                                    if (result.data.length > 0) {
                                        layer.msg("查询成功！");
                                        $('#DDUserId').val(result.data[0].dingresult.Result.Userid);
                                    } else {
                                        layer.msg("未查询到" + titles + "用户名！");
                                    }
                                } else {
                                    layer.msg(result.msg);
                                }
                                layer.close(indes);

                            }, error: function (res) {

                                layer.close(indes);
                            }
                        }
                    )
                }
                //下载Excel模板
                function downloadExcel() {

                    var method = 'post';//请求方法
                    var url = '/User/Download';//请求url
                    var xhr = new XMLHttpRequest();//定义一个XMLHttpRequest对象
                    xhr.open(method, url, true);
                    xhr.responseType = 'blob';//设置ajax的响应类型为blob
                    xhr.setRequestHeader('Content-Type', 'application/json;charset=utf-8');
                    xhr.onload = function ()//当请求完成，响应就绪进入
                    {
                        if (this.status == 200)//当响应状态码为200时进入
                        {
                            var blob = this.response;//获取响应返回的blob对象
                            //这一段用来判断是否是IE浏览器，因为下面有些代码不支持IE
                            if (typeof window.navigator.msSaveBlob !== 'undefined') {
                                window.navigator.msSaveBlob(blob);
                                return;
                            }
                            var a = document.createElement('a');//在dom树上创建一个a标签
                            var urlA = window.URL.createObjectURL(blob);//我的理解是生成一个相对于浏览器的虚拟url，用于指向传入的blob对象，让浏览器可以通过这个url找到这个blob对象
                            a.href = urlA;//将url赋值给a标签的href属性
                            a.download = '批量导入用户模板.xlsx';
                            a.click();//主动触发a标签点击事件
                            var reader = new window.FileReader();
                            reader.readAsDataURL(blob);
                        }
                    };

                    xhr.send();

                }

                //加载注册结果列表
                function tableRender(data) {
                    table.render({
                        elem: '#tablelist2'
                        , page: false
                        , limit: 1100
                        , toolbar: '#toolbarDemo2'
                        , cols: [[

                            { field: 'No', title: '序号', width: 60, fixed: 'left' },
                            { field: 'UserName', title: '用户名', width: 120, fixed: 'left' },
                            {
                                field: 'okMsg', title: '注册结果', fixed: 'left', width: 200, templet: function (d) {
                                    if (d.okMsg =="用户注册成功") {
                                        return '<span style="color:Blue;">' + d.okMsg + '</span>'
                                    } else {

                                        return '<span style="color:red;">' + d.okMsg + '</span>'
                                    }
                                }
                            },
                            { field: 'EmployeeNum', title: '工号', width: 120 },
                            { field: 'Email', title: '电子邮箱' },
                            { field: 'TrueName', title: '真实姓名', width: 120 },
                            { field: 'PhoneNumber', title: '手机号码', width: 150 },
                             { field: 'DDUserId', title: titles + '号', width: 140 }
                        ]]
                        , data: data
                    });
                }

                $("#btnPSK").click(function(){


                    $.get('/User/createPSK?UserName=' +  $("#txtUserName").val(), function (res) {

                        if (res.code == 0) {
                            $("#txtUrl").val(res.data.url);
                            $("#txtPSK").val(res.data.key);
                            $("#imgQRCode").attr("src", "data:image/png;base64," + res.data.qrCode);
                        }
                        else {
                            layer.msg(res.msg);
                        }
                    })

                })

                $("#btnRefresh").click(function(){
                    var name = $("#txtUserName").val();
                     layer.confirm('确定要刷新用户【'+name+'】的PSK吗?', {
                        title: '',
                        btn: ['确定', '取消'], //按钮
                        resize: false

                    }, function (index) {
                        $.get('/User/refreshPSK?UserName=' +  $("#txtUserName").val(), function (res) {

                        if (res.code == 0) {
                            $("#txtUrl").val(res.data.url);
                            $("#txtPSK").val(res.data.key);
                            $("#imgQRCode").attr("src", "data:image/png;base64," + res.data.qrCode);
                                layer.close(index);
                        }
                        else {
                            layer.msg(res.msg);
                        }
                    })
                    })



                })

                ///修改弹出页面xmselect
                var xmDeptsListUpdate = xmSelect.render({
                    el: '#xmDeptsListUpdate',
                    model: { label: { type: 'text' } },
                    prop: {
                        name: 'title',
                        value: 'id',
                    },
                    minWidth: 200,
                    radio: true,
                    filterable: true,
                    clickClose: true,
                    //树
                    tree: {
                        show: true,
                        //非严格模式
                        strict: false,
                        //默认展开节点
                        expandedKeys: [-1],
                    },
                    data: []
                });

                // var xmDeptsListNew = xmSelect.render({
                //     el: '#xmDeptsListNew',
                //     model: { label: { type: 'text' } },
                //     prop: {
                //         name: 'title',
                //         value: 'id',
                //     },
                //     minWidth: 200,
                //     radio: true,
                //     filterable: true,
                //     clickClose: true,
                //     //树
                //     tree: {
                //         show: true,
                //         //非严格模式
                //         strict: false,
                //         //默认展开节点
                //         expandedKeys: [-1],
                //     },
                //     data: []
                // });

                function GetDeptsTree(value) {
                    $.ajax({
                        url: '/CommAPI/GetOrgsTreeList',
                        type: "post",
                        datatype: 'json',
                        data: { 'Type': value },
                        success: function (result) {
                            xmDeptsListUpdate.update({
                                data: result
                            });

                            xmDeptsListUpdate.setValue([0]);

                            xmDeptsListNew.update({
                                data: result
                            });

                        }, error: function () {
                            layer.msg("获取失败！");
                        }
                    })
                };
                SearchData();

            });
        </script>
        <script type="text/html" id="switchTpl">
            <!-- 这里的 checked 的状态只是演示 -->
            <input type="checkbox" id="switch" name="{{d.Id}}" value="{{d.StopUsing}}" lay-skin="switch" lay-text="停用|启用" lay-filter="sexDemo" {{ d.StopUsing == 'on' ? 'checked' : '' }}>
        </script>
        <script type="text/html" id="checkboxTpl">
            <!-- 这里的 checked 的状态只是演示 -->
            <input type="checkbox" id="lockbox" name="{{d.Id}}" value="{{d.IsLockedOut}}" title="锁定" lay-filter="lockDemo" {{ d.IsLockedOut == 'on' ? 'checked' : '' }}>
        </script>
    </div>
</body>

<!-- 用户新增 -->
<div class="window_wrap" id="form_window" lay-filter="form_window" style="display: none">
    <form class="layui-form" lay-filter="fm" id="fm" action="">
        <div class="layui-row form_wrap layui-form" lay-filter="filterInfo" id="filterInfo">
            <div class="layui-form-item">
                <div class="form_item">
                    <label class="layui-form-label">工号</label>
                    <div class="layui-input-block">
                        <input type="text" name="EmployeeNum" id="EmployeeNum" lay-verify="required" placeholder="请输入工号" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form_item">
                    <label class="layui-form-label">真实姓名</label>
                    <div class="layui-input-block">
                        <input type="text" name="TrueName" id="TrueName" lay-verify="required" placeholder="请输入真实姓名" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="form_item">
                    <label class="layui-form-label">手机号码</label>
                    <div class="layui-input-block">
                        <input type="text" name="PhoneNumber" id="PhoneNumber" placeholder="请输入手机号码" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="form_item">
                    <label class="layui-form-label">用户名</label>
                    <div class="layui-input-block ">
                        <input type="text" name="Id" id="Id" style="display:none;" />
                        <input type="text" name="UserName" id="UserName" lay-verify="usernameck" placeholder="请输入用户名" autocomplete="off" class="layui-input layui-input-inline min_width">
                        <button type="button" id="ckuser" class="layui-btn layui-btn-xs layui-btn-normal min_top">检测</button>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="form_item">
                    <label class="layui-form-label">电子邮箱</label>
                    <div class="layui-input-block ">
                        <input type="text" name="Email" id="Email" placeholder="请输入电子邮箱" autocomplete="off" class="layui-input layui-input-inline min_width">
                        <button type="button" id="ckemail" class="layui-btn layui-btn-xs layui-btn-normal min_top">检测</button>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="form_item">
                    <label class="layui-form-label">@{
                            @ViewBag.titles
                        }号</label>
                    <div class="layui-input-block ">
                        <input type="text" name="DDUserId" id="DDUserId" placeholder="请输入@{
                            @ViewBag.titles
}号" autocomplete="off" class="layui-input layui-input-inline min_width">
                        <button type="button" id="ckding" class="layui-btn layui-btn-xs layui-btn-normal min_top">查找</button>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="form_item" style="min-width:300px;">
                    <div class="layui-inline">
                        <label class="layui-form-label">停用状态</label>
                        <div class="layui-input-block">
                            <input type="checkbox" name="StopUsing" id="StopUsing" lay-skin="switch" lay-text="ON|OFF">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:56px;">锁定状态</label>
                        <div class="layui-input-block">
                            <input type="checkbox" name="IsLockedOut" id="IsLockedOut" lay-skin="switch" lay-text="ON|OFF">
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="form_item">
                    <label class="layui-form-label">锁定到</label>
                    <div class="layui-input-block">
                        <input type="text" name="LockoutEnd" id="LockoutEnd" class="layui-input layui-input-inline" />
                    </div>
                </div>
            </div>
          


        </div>
         <div class ="layui-form-item" style="display:none">
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit lay-filter="submit" id="submit" style="display:none">提交</button>
                <button type="reset" class="layui-btn layui-btn-primary" id="btn_reset" lay-filter="btn_reset" style="display:none">重置</button>

            </div>

        </div>  
     
    </form>
</div>

<!-- 批量导入用户 -->
<div class="window_wrap" id="form_window_Import" style="display: none">
    <div class="layui-card">
        <div class="layui-card-header">
            <div class="layui-inline">
                <div class="layui-input-inline">
                    <button class="layui-btn layui-btn-normal fr" id="download">下载Excel模板</button>
                    <button class="layui-btn layui-btn" id="addFileBtn">上传Excel文件</button>
                    <span style="color:red;display:none" id="UploadSpan">每个用户注册耗时2至3秒，导入用户过多时，请耐心等待,不要进行其他操作。</span>
                </div>
            </div>

        </div>
        <div class="layui-card-body">
            <table id="tablelist2" lay-filter="tablelist2"></table>

        </div>
    </div>
</div>

<!-- 用户修改 -->
<div class="window_wrap" id="updateform_window" style="display: none">
    <form class="layui-form" lay-filter="fmupdate" id="fmupdate" action="">
        <div class="layui-form-item">
            <label class="layui-form-label">用户名</label>
            <div class="layui-input-block">
                <input type="text" name="Id" id="Id_up" style="display:none;" />
                <input type="text" name="UserName" id="UserName_up" required lay-verify="required" placeholder="请输入用户名" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">电子邮箱</label>
            <div class="layui-input-block">
                <input type="text" name="Email" id="Email_up" placeholder="请输入电子邮箱" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item" style="display:none">
            <label class="layui-form-label">医学中心</label>
            <div class="layui-input-block">
                <div id="xmDeptsListUpdate"></div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">工号</label>
            <div class="layui-input-block">
                <input type="text" name="EmployeeNum" id="EmployeeNum_up" placeholder="请输入工号" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">真实姓名</label>
            <div class="layui-input-block">
                <input type="text" name="TrueName" id="TrueName_up" required lay-verify="required" placeholder="请输入真实姓名" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">手机号码</label>
            <div class="layui-input-block">
                <input type="text" name="PhoneNumber" id="PhoneNumber_up" placeholder="请输入手机号码" autocomplete="off" class="layui-input">
            </div>
        </div>
       <div class="layui-form-item">
            <label class="layui-form-label">@{@ViewBag.titles}号</label>
            <div class="layui-input-block ">
                <input type="text" name="DDUserId" id="DDUserId_up" lay-verify="DDUserId" placeholder="请输入@{@ViewBag.titles}号" autocomplete="off" class="layui-input">
            </div>
        </div>  

        <div class="layui-form-item">
            <label class="layui-form-label">停用状态</label>
            <div class="layui-input-block">
                <input type="checkbox" name="StopUsing" id="StopUsing" lay-skin="switch" lay-text="ON|OFF">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">锁定状态</label>
            <div class="layui-input-block">
                <input type="checkbox" name="IsLockedOut" id="IsLockedOut" lay-skin="switch" lay-text="ON|OFF">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">锁定到</label>
            <div class="layui-input-block">
                <input type="text" name="LockoutEnd" id="LockoutEnd" class="layui-input" />
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit lay-filter="submitupdate">保存</button>
            </div>
        </div>
    </form>
</div>

<!-- 菜单设置 -->
<div class="" id="Purview_window" style="display:none">
    <div class="layui-card">
        <div class="layui-card-body layui-form flex_between">
            <div class="tools_l">
                <div class="layui-inline">
                    <div id="xmDeptsList" style="width:250px"></div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">限制专病组:</label>
                    <input type="checkbox" id="mySwitch" name="mySwitch" lay-skin="switch" lay-filter="mySwitch">
                </div>
                <div class="layui-inline" id="Glist" style="display:none">
                    <div id="xmGroupList" style="width:250px"></div>
                </div>
            </div>
            <div class="tools_r">
                <button class="layui-btn layui-btn-normal" id="AddPurview">添 &nbsp;&nbsp;加</button>
            </div>
        </div>
        <div class="layui-card-body">
            <table id="Purviewlist" lay-filter="Purviewlist"></table>
            <script type="text/html" id="tablePurviewBar">
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="PurviewDel"><i class="layui-icon layui-icon-delete"></i>删除</a>
            </script>
        </div>
    </div>
</div>

<div class="" id="Role_window" style="display: none">
    @*<div id="xmSelectRoleId" class="xm-select-RoleId"></div>*@

    <div id="test4" class="demo-transfer flex_center" style="margin:10px"></div>
</div>
<div class="layui-input-inline" id="Role_Lock_window" style="display: none">
    <div class="layui-input-inline">
    <label class="layui-form-label">锁定到</label>
    <div class="layui-input-inline">
        <input style="width:200px;" type="text" name="RLockoutEnd" id="RLockoutEnd" class="layui-input">
        </div>
        </div> 
    <div class="layui-input-inline">
    <button type="button" id="LockStatus" class="layui-btn layui-btn-lg">确定</button>
        </div>
</div>


<!-- psk -->
<div class="window_wrap" id="window_psk" style="display: none">
    <form class="layui-form" lay-filter="form_psk" id="form_psk" action="">
        <div class="layui-form-item">
            <label class="layui-form-label">PSK</label>
            <div class="layui-input-inline" style="width:310px!important">
                <input type="text" name="txtUrl" id="txtUrl" readonly class="layui-input">
                <input type="hidden" name="txtPSK" id="txtPSK" readonly class="layui-input">
                <input type="hidden" name="txtUserName" id="txtUserName" readonly class="layui-input">
            </div>
            <div class="layui-form-mid" style="padding: 0!important;">
                <button type="button" class="layui-btn layui-btn-primary" id="btnPSK">获取</button>
                <button type="button" class="layui-btn layui-btn-primary" id="btnRefresh">刷新</button>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">二维码</label>
            <div class="layui-input-block">
                <img id="imgQRCode" height="300px" width="300px" />
            </div>
        </div>
    </form>
</div>
</html>
