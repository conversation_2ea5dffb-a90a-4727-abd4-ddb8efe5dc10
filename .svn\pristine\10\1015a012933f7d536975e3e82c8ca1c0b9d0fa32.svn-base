﻿using AngelwinFollowUp.Models;
using AngelwinFollowUp.Web.Filters;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System.Dynamic;
using System.Text.Json;
using System.Xml.Linq;

namespace AngelwinFollowUp.Web.Areas.AIFollowUp.Controllers
{
    [Authorizing]
    [Area("AIFollowUp")]
    public class FollowUpTemplatesController : Controller
    {
        private IConfiguration Configuration { get; }

        private readonly AngelwinFollowUpDbContext db;
        public FollowUpTemplatesController(IConfiguration configuration, AngelwinFollowUpDbContext _db)
        {
            Configuration = configuration;
            db = _db;
        }
        public IActionResult Index()
        {
            return View();
        }

        public IActionResult List(int page, int limit, string keyWord)
        {
            var totalCount = 0;
            var resultResult = new List<dynamic>();
            var query = db.FollowupTemplates.AsQueryable();
            if (!string.IsNullOrWhiteSpace(keyWord))
            {
                keyWord = keyWord.Trim();
                query = query.Where(o => o.TemplateName.Contains(keyWord)||o.DiseaseCode.Contains(keyWord)).AsQueryable();
            }
            totalCount = query.Count();
            var list = query.OrderBy(o => o.Id).Skip((page - 1) * limit).Take(limit).ToList();
            if (list != null && list.Any())
            {
                //var centers = db.FollowupTemplates.AsQueryable();
                foreach (var item in list)
                {
                    dynamic result = new ExpandoObject();
                    result.Id = item.Id;
                    result.TemplateName = item.TemplateName;
                    result.DiseaseCode = item.DiseaseCode;
                    result.Followup = item.Followup;
                    result.Remark = item.Remark;
                    result.CreateUserName = item.CreateUserName;
                    result.CreatedTime = item.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss");
                    resultResult.Add(result);
                }
            }
            // 使用 System.Text.Json 的配置
            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = null // 保持属性名称原样输出（默认行为）
            };
            return Json(new { code = "0", msg = "成功", count = totalCount, data = resultResult }, options);
        }

        public IActionResult DetailList(int page, int limit, int templeteId)
        {
            var totalCount = 0;
            var resultResult = new List<dynamic>();
            var query = db.FollowupTemplateDetails.Where(o => o.TemplateId == templeteId).AsQueryable();

            totalCount = query.Count();
            var list = query.OrderBy(o => o.Orderby).Skip((page - 1) * limit).Take(limit).ToList();
            if (list != null && list.Any())
            {
                foreach (var item in list)
                {
                    dynamic result = new ExpandoObject();
                    result.Id = item.Id;
                    result.TemplateDetailName = item.TemplateDetailName;
                    result.Methods = item.Methods;
                    result.TemplateId = item.TemplateId;
                    result.Description = item.Description;
                    result.FollowupPoint = item.FollowupPoint;
                    result.DelayDay = item.DelayDay;
                    result.Orderby = item.Orderby;
                    result.DetailRemark = item.Remark;
                    result.CreateUserName = item.CreateUserName;
                    result.CreatedTime = item.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss");
                    resultResult.Add(result);
                }
            }
            // 使用 System.Text.Json 的配置
            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = null // 保持属性名称原样输出（默认行为）
            };
            return Json(new { code = "0", msg = "成功", count = totalCount, data = resultResult }, options);
        }


        [HttpPost]
        public IActionResult Save(FollowupTemplate node)
        {
            try
            {

                var msg = "";
                if (node.TemplateName == null)
                {
                    return Json(new { code = -1, errorMsg = "模板名称不能为空！" });
                }
                if (node.Id == 0)
                {
                    node.CreateUserName = User.Identity.Name + "";
                    node.DiseaseCode = node.DiseaseCode + "";
                    node.Remark = node.Remark + "";
                    db.FollowupTemplates.Add(node);
                    msg = "创建成功。";
                }
                else
                {
                    var entity = db.FollowupTemplates.FirstOrDefault(o => o.Id == node.Id);
                    if (entity == null)
                        return Json(new { okMsg = "数据不存在！" });
                    entity.TemplateName = node.TemplateName + "";
                    entity.DiseaseCode = node.DiseaseCode + "";
                    entity.Followup = node.Followup;
                    entity.Remark = node.Remark+"";
                    entity.CreateUserName = User.Identity.Name;
                    entity.CreatedTime = System.DateTime.Now;
                    msg = "修改成功。";
                }
                db.SaveChanges();
                return Json(new { code = 0, okMsg = msg });
            }
            catch (System.Exception ex)
            {
                return Json(new { code = -1, errorMsg = "保存数据时出错。" + ex.Message });
            }
        }

        [HttpPost]
        public IActionResult DetailSave(FollowupTemplateDetail node)
        {
            try
            {
                var msg = "";
                if (node.TemplateDetailName == null)
                {
                    return Json(new { code = -1, errorMsg = "模板详情名称不能为空！" });
                }
                if (node.Id == 0)
                {
                    node.CreateUserName = User.Identity.Name + "";
                    node.Description = node.Description + "";
                    node.Methods = "电话";
                    node.Remark = node.Remark + "";
                    node.DelayDay = 0;
                    db.FollowupTemplateDetails.Add(node);
                    msg = "创建成功。";
                }
                else
                {
                    var entity = db.FollowupTemplateDetails.FirstOrDefault(o => o.Id == node.Id);
                    if (entity == null)
                        return Json(new { okMsg = "数据不存在！" });
                    entity.TemplateDetailName = node.TemplateDetailName;
                    entity.TemplateId = node.TemplateId;
                    entity.Methods = node.Methods + "";
                    entity.Description = node.Description + "";
                    entity.FollowupPoint = node.FollowupPoint + "";
                    entity.DelayDay = node.DelayDay;
                    entity.Orderby = node.Orderby;
                    entity.Remark = node.Remark + "";
                    entity.CreateUserName = User.Identity.Name;
                    entity.CreatedTime = System.DateTime.Now;
                    msg = "修改成功。";
                }
                db.SaveChanges();
                return Json(new { code = 0, okMsg = msg });
            }
            catch (System.Exception ex)
            {
                return Json(new { code = -1, errorMsg = "保存数据时出错。" + ex.Message });
            }
        }

        [HttpPost]
        public IActionResult Create(FollowupTemplate node)
        {
            node.Followup = 0;
            node.CreateUserName = User.Identity.Name;
            node.CreatedTime = System.DateTime.Now;
            if (ModelState.IsValid)
            {

                db.FollowupTemplates.Add(node);
                try
                {
                    db.SaveChanges();
                    return Json(new { okMsg = "创建成功。" });
                }
                catch (Exception ex)
                {
                    return Json(new { errorMsg = "保存时数据出错。" });
                }
            }
            else
            {
                return Json(new { errorMsg = "数据验证错误。" });
            }

        }

        [HttpPost]
        public IActionResult Edit(FollowupTemplate node)
        {
            if (ModelState.IsValid)
            {
                var entity = db.FollowupTemplates.FirstOrDefault(o => o.Id == node.Id);
                var count = db.FollowupTemplateDetails.Where(o => o.TemplateId == node.Id).Count();

                entity.TemplateName = node.TemplateName;
                entity.DiseaseCode = node.DiseaseCode;
                entity.Followup = count;
                entity.Remark = node.Remark;
                entity.CreateUserName = User.Identity.Name;
                entity.CreatedTime = System.DateTime.Now;
                try
                {
                    db.SaveChanges();
                    return Json(new { okMsg = "修改成功。" });
                }
                catch (Exception ex)
                {
                    return Json(new { errorMsg = "保存数据时出错。" + ex.Message });
                }
            }
            else
            {
                return Json(new { errorMsg = "数据验证错误。" });
            }
        }

        [HttpPost]
        public IActionResult Del(int id)
        {
            try
            {
                var node = db.FollowupTemplates.Where(a => a.Id == id).FirstOrDefault();

                db.Entry(node).State = EntityState.Deleted;
                db.SaveChanges();
                return Json(new { okMsg = "删除成功。" });
            }
            catch (Exception ex)
            {
                var error = ex.InnerException?.Message;
                return Json(new { errorMsg = $"随访模板下已生成随访详情，不能删除。请联系管理员！" });
            }
        }

        [HttpPost]
        public IActionResult DetailDel(int id)
        {
            try
            {
                var node = db.FollowupTemplateDetails.Where(a => a.Id == id).FirstOrDefault();

                db.Entry(node).State = EntityState.Deleted;
                db.SaveChanges();
                return Json(new { okMsg = "删除成功。" });
            }
            catch (Exception ex)
            {
                var error = ex.InnerException?.Message;
                return Json(new { errorMsg = $"随访模板详情已生成随访计划，不能删除。请联系管理员！" });
            }
        }
    }
}
