{"format": 1, "restore": {"D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\AngelwinFollowUp.Web.csproj": {}}, "projects": {"D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Library\\AngelwinFollowUp.Library.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Library\\AngelwinFollowUp.Library.csproj", "projectName": "AngelwinFollowUp.Library", "projectPath": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Library\\AngelwinFollowUp.Library.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Library\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["D:\\work space\\project\\三部\\AI随访\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://8.131.88.44:5091/nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.AspNetCore.Mvc.Core": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.3, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\AngelwinFollowUp.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\AngelwinFollowUp.Web.csproj", "projectName": "AngelwinFollowUp.Web", "projectPath": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\AngelwinFollowUp.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["D:\\work space\\project\\三部\\AI随访\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://8.131.88.44:5091/nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Library\\AngelwinFollowUp.Library.csproj": {"projectPath": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Library\\AngelwinFollowUp.Library.csproj"}, "D:\\work space\\project\\三部\\AI随访\\Common.Tools\\Common.Tools.csproj": {"projectPath": "D:\\work space\\project\\三部\\AI随访\\Common.Tools\\Common.Tools.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Common.DataSourceSupport": {"target": "Package", "version": "[1.1.35, )"}, "EPPlus": {"target": "Package", "version": "[7.7.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.3, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "SharpToken": {"target": "Package", "version": "[2.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "D:\\work space\\project\\三部\\AI随访\\Common.Tools\\Common.Tools.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\work space\\project\\三部\\AI随访\\Common.Tools\\Common.Tools.csproj", "projectName": "Common.Tools", "projectPath": "D:\\work space\\project\\三部\\AI随访\\Common.Tools\\Common.Tools.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\work space\\project\\三部\\AI随访\\Common.Tools\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["D:\\work space\\project\\三部\\AI随访\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://8.131.88.44:5091/nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Logging.Log4Net.AspNetCore": {"target": "Package", "version": "[8.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Otp.NET": {"target": "Package", "version": "[1.4.0, )"}, "QRCoder": {"target": "Package", "version": "[1.4.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}