﻿@{
	Layout = null;
}

<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>随访模板管理</title>
	<link href="~/layuiadmin/layui/css/layui.css" rel="stylesheet" />
	<link href="~/css/simditor.css" rel="stylesheet" />
	<script type="text/javascript" src="~/js/jquery.min.js"></script>
	<script type="text/javascript" src="~/js/module.js"></script>
	<script type="text/javascript" src="~/js/uploader.js"></script>
	<script type="text/javascript" src="~/js/module.min.js"></script>
	<script type="text/javascript" src="~/js/hotkeys.min.js"></script>
	<script type="text/javascript" src="~/js/uploader.min.js"></script>
	<script type="text/javascript" src="~/js/simditor.min.js"></script>
	<script type="text/javascript" src="~/js/simditor.js"></script>
	<style>
		/*弹窗*/
		.window_wrap {
			padding: 15px;
		}

		.search_wrap {
			padding: 20px;
		}

		.layui-tab-brief {
			background-color: #fff;
		}

		.line_wrap {
			padding: 10px 15px;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
		}

		.form_item {
			padding-top: 10px;
		}

		.btnwrap {
			padding-left: 10px;
		}

		.layui-show {
			display: flex !important;
			align-items: flex-end;
		}

		.form_wrap {
			flex: 1;
		}

		.table_wrap {
			overflow: hidden;
		}

		#detail_window .layui-form-label {
			width: 100px;
		}

		#detail_window .layui-form-val {
			padding: 9px 15px;
		}

		#detail_window .mindow_icon .title_icon {
			display: inline-block;
		}

		#detail_window .mindow_icon {
			text-align: center;
			padding: 20px 0;
		}

		#detail_window .layui-form-item {
			margin-bottom: 0;
		}
		/* 定义表头样式 */
		.layui-table-header .layui-table-cell {
			font-weight: bold; /* 加粗 */
			font-size: 14px; /* 可选：调整字体大小以提升可读性 */
			color: #333; /* 可选：调整字体颜色 */
		}
	</style>
</head>

<body style="padding:5px;">
	<div class="layui-col-md12">
		<div class="layui-card">
			<!--搜索区-->
			<div class="line_wrap search_wrap">
				<div>
					<div class="layui-inline">
						<label class="layui-form-label">搜索条件</label>
						<div class="layui-input-inline">
							<input type="text" class="layui-input" name="keyWord" placeholder="请输入关键字" id="keyWord" value="" />
						</div>
						<button id="Search" class="layui-btn layui-btn-primary"><i class="layui-icon layui-icon-search"></i> </button>
					</div>
				</div>
				<div>
					<button type="button" id="addTemplate" data-type="add" class="layui-btn layui-btn-normal"><i class="layui-icon layui-icon-addition"></i></button>
				</div>
			</div>

			<div class="layui-row">
				<div class="layui-col-xs6">
					<div class="layui-card-body table_wrap" id="List">
						<table id="tablelist" lay-filter="tablelist"></table>
						<script type="text/html" id="tableBar">
							<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="addDetail"><i class="layui-icon layui-icon-addition"></i>新增详情</a>
							<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="update"><i class="layui-icon layui-icon-edit"></i>编辑</a>
							<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del" style="text-decoration:none"><i class="layui-icon layui-icon-delete"></i>删除</a>
						</script>
					</div>
				</div>
				<div class="layui-col-xs6">
					<div class="layui-card-body table_wrap">
						<table id="detaillist" lay-filter="detaillist"></table>
						<script type="text/html" id="tableDeltailBar">
							<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="update"><i class="layui-icon layui-icon-edit"></i>编辑</a>
							<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del" style="text-decoration:none"><i class="layui-icon layui-icon-delete"></i>删除</a>
						</script>
					</div>
				</div>

			</div>
		</div>
	</div>

	<!--新增模板-->
	<div class="window_wrap" id="Template_window" style="display: none">
		<form class="layui-form" lay-filter="fm1" id="fm1" action="">
			<div class="layui-form-item">
				<label class="layui-form-label">模板名称</label>
				<div class="layui-input-block">
					<input type="text" name="Id" id="Id" style="display:none;" />
					<input type="text" name="TemplateName" id="TemplateName" lay-verify="required" placeholder="请输入模板名称" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">编码</label>
				<div class="layui-input-block">
					<input type="text" name="DiseaseCode" id="DiseaseCode" lay-verify="required" placeholder="请输入疾病/手术编码" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">随访次数</label>
				<div class="layui-input-block">
					<input type="text" name="Followup" id="Followup" lay-verify="required" placeholder="请输入随访次数" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">备注</label>
				<div class="layui-input-block">
					<textarea name="Remark" id="Remark" class="layui-textarea" style="resize: none"></textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-input-block">
					<button type="button" class="layui-btn" lay-submit lay-filter="submit">提交</button>
					<button type="reset" class="layui-btn layui-btn-primary" id="btn_reset">重置</button>
				</div>
			</div>
		</form>
	</div>
	<!--新增模板详情-->
	<div class="window_wrap" id="TemplateDetail_window" style="display: none">
		<form class="layui-form" lay-filter="fm1" id="fm1" action="">
			<div class="layui-form-item">
				<label class="layui-form-label">详情名称</label>
				<div class="layui-input-block">
					<input type="text" name="DetailId" id="DetailId" style="display:none;" />
					<input type="text" name="TemplateId" id="TemplateId" style="display:none;" />
					<input type="text" name="TemplateDetailName" id="TemplateDetailName" lay-verify="required" placeholder="请输入模板详情名称" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item" style="display:none;">
				<label class="layui-form-label">随访方式</label>
				<div class="layui-input-block">
					<input type="text" name="Methods" id="Methods" placeholder="请输入随访方式" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item" >
				<label class="layui-form-label">随访时间</label>
				<div class="layui-input-block">
					<input name="FollowupPoint" id="FollowupPoint" lay-verify="required" placeholder="选择日期" type="text" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item" style="display:none;">
				<label class="layui-form-label">延迟天数</label>
				<div class="layui-input-block">
					<input type="text" name="DelayDay" id="DelayDay" placeholder="请输入延迟天数" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">排序</label>
				<div class="layui-input-block">
					<input type="text" name="Orderby" id="Orderby" lay-verify="required" placeholder="请输入排序号" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">随访问题</label>
				<div class="layui-input-block">
					<textarea name="Description" id="Description" placeholder="请输入随访描述" class="layui-textarea" style="resize: none; min-height: 65vh; width: 100%;"></textarea>
					@*<script>
							var Description = new Simditor({
							textarea: $("#Description")
						});
					</script>
					<input type="text" name="Description" id="Description" lay-verify="required" placeholder="请输入随访描述" autocomplete="off" class="layui-input"> *@
				</div>
			</div>
			
			<div class="layui-form-item" style="display:none;">
				<label class="layui-form-label">备注</label>
				<div class="layui-input-block">
					<textarea name="DetailRemark" id="DetailRemark" class="layui-textarea" style="resize: none"></textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-input-block">
					<button type="button" class="layui-btn" lay-submit lay-filter="Detailsubmit">提交</button>
					<button type="reset" class="layui-btn layui-btn-primary" id="btn_reset">重置</button>
				</div>
			</div>
		</form>
	</div>
	<script src="~/js/jquery-3.5.1.min.js"></script>
	<script src="~/layuiadmin/layui/layui.js"></script>
	<script src="~/layuiadmin/layuiextend/xm-select.js"></script>

	<script>
		layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
			var element = layui.element
				, layer = layui.layer
				, table = layui.table//表格
				, laydate = layui.laydate
				, $ = layui.$
				, form = layui.form;
			;
			var url = ''
			var TempleName ='';
			 // 初始化日期选择器
			laydate.render({
				elem: '#FollowupPoint' // 绑定元素
				, type: 'date'
				, format: 'yyyy-MM-dd' // 设置日期格式
				, done: function (value, date) {
				}
			});
			table.render({
				elem: '#tablelist'
				, id: 'tablelist'
				, page: true

				, limit: 20
				, height: 'full-105'
				, cols: [[
					{ field: 'No', title: '序号', type: 'numbers', fixed: 'left' }
					, { field: 'TemplateName', title: '模板名称', width: 130, minWidth: 128 }
					, { field: 'DiseaseCode', title: '疾病/手术编码', width: 120, minWidth: 118 }
					, { field: 'Followup', title: '随访次数', width: 90, minWidth: 86 }
					, { field: 'Remark', title: '备注', width: 200, minWidth: 200}
					, { field: 'CreateUserName', title: '创建人' , width: 75, minWidth: 72 }
					, { field: 'CreatedTime', title: '创建时间' }
					, { title: '操作', toolbar: '#tableBar', width: 235, minWidth: 235, fixed: 'right' }
				]]
				, done: function (res, curr, count) {
					var id = 0;
					if (res.data.length > 0) {
						//默认第一行选中
						$("#List .layui-table tbody").find('tr').attr({ "style": "background:white" });
						$("#List .layui-table tbody").find("tr:eq(0)").attr({ "style": "background:#5FB878" });//默认第一行选中
						id = res.data[0].Id;
						TempleName = res.data[0].TemplateName;

					}
					SearchDetailData(id);
					table.on('row(tablelist)', function (obj) {
						obj.tr.parents('tbody').find('tr').attr({ "style": "background:white" });
						obj.tr.attr({ "style": "background:#5FB878" });  //被选中的行变色
						//加载Detail表格
						SearchDetailData(obj.data.Id);
						TempleName = obj.data.TemplateName;

					});
				}
			});

			//监听tablelist工具条
			table.on('tool(tablelist)', function (obj) {
				var data = obj.data;
				if (obj.event === 'del') {
					layer.confirm('确定要删除名为【' + data.TemplateName + '】的模板吗？将无法恢复。', {
						title: '',
						btn: ['确定', '取消'], //按钮
						resize: false
					}, function (index) {
							$.post('/AIFollowUp/FollowUpTemplates/Del', { id: data.Id }, function (result) {
							if (result.okMsg) {
								layer.msg(result.okMsg);
								currentIndex = -1;
								table.reload('tablelist'); //重载表格
								EmptyData();
							} else {
								layer.msg(result.errorMsg);
							}
						}, 'json');
						layer.close(index);
					});
				}
				else if (obj.event === 'addDetail') {
						//Description.setValue('');
						TempleName = data.TemplateName;
						$('#DetailId').val('');
						$("#TemplateDetailName").val('');
						$("#Methods").val('');
						$("#FollowupPoint").val('');
						$("#DelayDay").val('');
						$("#Description").val('');
						$("#Orderby").val('');
						$("#DetailRemark").val('');
						$("#TemplateId").val(data.Id);
						windowsIndex = layer.open({
							type: 1,
							title: '【'+TempleName+'】新增模板详情信息',
							area: ['100%', '100%'],
							resize: true,
							content: $('#TemplateDetail_window')
						});
						url = '/AIFollowUp/FollowUpTemplates/DetailSave';
					}
				else if(obj.event === 'update') {
						TempleName = data.TemplateName;
						$("#TemplateName").val(data.TemplateName);
						$("#DiseaseCode").val(data.DiseaseCode);
						$("#Followup").val(data.Followup);
						$("#Remark").val(data.Remark);
						$("#Id").val(data.Id);
						windowsIndex = layer.open({
							type: 1,
							title: '【'+TempleName+'】修改模板信息',
							area: ['40%', '47%'],
							resize: true,
							content: $('#Template_window')
						});
						url = '/AIFollowUp/FollowUpTemplates/Save';
					}
			});

			table.render({
				elem: '#detaillist'
				, id: 'detaillist'
				, page: true

				, limit: 20
				, height: 'full-105'
				, cols: [[
					{ field: 'No', title: '序号', type: 'numbers', fixed: 'left' }
					, { field: 'TemplateDetailName', title: '详情名称', width: 100, minWidth: 100 }
					, { field: 'Methods', title: '随访方式', width: 90, minWidth: 86 }
					, { field: 'FollowupPoint', title: '随访时间', width: 105, minWidth: 103 }
					, { field: 'DelayDay', title: '延迟天数', width: 90, minWidth: 86 }
					, { field: 'Description', title: '随访描述', width: 230, minWidth: 230  }
					, { field: 'Orderby', title: '排序', width: 100, minWidth: 100 }
					, { field: 'DetailRemark', title: '备注', width: 100, minWidth: 100 }
					, { field: 'CreateUserName', title: '创建人' , width: 75, minWidth: 72 }
					, { field: 'CreatedTime', title: '创建时间',width: 160, minWidth: 160 }
					, { title: '操作', toolbar: '#tableDeltailBar', width: 160, minWidth: 160, fixed: 'right' }
				]]
				, done: function (res, curr, count) {
					var id = 0;
				}
			});

			//监听detaillist工具条
			table.on('tool(detaillist)', function (obj) {
				var data = obj.data;
				if (obj.event === 'del') {
					layer.confirm('确定要删除名为【' + data.TemplateDetailName + '】的详情模板吗？将无法恢复。', {
						title: '',
						btn: ['确定', '取消'], //按钮
						resize: false
					}, function (index) {
							$.post('/AIFollowUp/FollowUpTemplates/DetailDel', { id: data.Id }, function (result) {
							if (result.okMsg) {
								layer.msg(result.okMsg);
								currentIndex = -1;
								table.reload('detaillist'); //重载表格
								EmptyData();
							} else {
								layer.msg(result.errorMsg);
							}
						}, 'json');
						layer.close(index);
					});
				}
				else if(obj.event === 'update') {
						//Description.setValue(data.Description);
						$("#TemplateDetailName").val(data.TemplateDetailName);
						$("#Methods").val(data.Methods);
						$("#TemplateId").val(data.TemplateId);
						$("#FollowupPoint").val(data.FollowupPoint);
						$("#DelayDay").val(data.DelayDay);
						$("#Description").val(data.Description);
						$("#Orderby").val(data.Orderby);
						$("#DetailRemark").val(data.DetailRemark);
						$("#DetailId").val(data.Id);
						//Description.sync(); // 必须调用 sync() 更新编辑器显示
						windowsIndex = layer.open({
							type: 1,
							title: '【'+TempleName+'】修改模板详情信息',
							area: ['100%', '100%'],
							resize: true,
							content: $('#TemplateDetail_window')
						});
						url = '/AIFollowUp/FollowUpTemplates/DetailSave';
					}
			});

			function SearchDetailData(Id) {

				table.reload('detaillist', {
					page: {
					   curr: 1
					},
					url: '/AIFollowUp/FollowUpTemplates/DetailList'
					, where: {
						'templeteId': Id
					}
				});
			};

			function SearchData() {

				table.reload('tablelist', {
					page: {
						curr: 1
					},
					url: '/AIFollowUp/FollowUpTemplates/List'
					, where: {
						'keyWord': $.trim($("#keyWord").val()),
					}
				});
			};			

			$(document).ready(function () {
				$('.layui-tab-title li').on('click', function () {
					// 获取被点击列表项的 value
					EmptyData();
					SearchData();
				})
				$(document).on('click', '#Search', function () {
					EmptyData();
					SearchData();
				})

				$(document).on('click', '#addTemplate', function () {
					EmptyData();
					windowsIndex = layer.open({
						type: 1,
						title: '新增模板信息',
						area: ['40%', '47%'],
						resize: true,
						content: $('#Template_window')
					});
					url = '/AIFollowUp/FollowUpTemplates/Save';
				});

			});
			function EmptyData() {
				$('#Id').val('');
				$("#TemplateName").val('');
				$("#DiseaseCode").val('');
				$("#Followup").val('');
				$("#Remark").val('');
			}
			SearchData();

			//监听提交
			form.on('submit(submit)', function (data) {
				var indes = layer.load(1);
				data.field.templateName = $("#TemplateName").val();
				data.field.diseaseCode = $("#DiseaseCode").val();
				data.field.followup = $("#Followup").val();
				data.field.remark = $("#Remark").val();
				data.field.id = $("#Id").val();
				if (data.field.templateName == '') { layer.msg("模板名称不能为空！"); layer.close(indes); return false; }
				if (data.field.diseaseCode == '') { layer.msg("疾病/手术编码不能为空！"); layer.close(indes); return false; }
				if (!/^\d+$/.test(data.field.followup)) {
					layer.msg("随访次数必须为非负整数！");
					layer.close(indes);
					return false;
				}
				//提交 Ajax 成功后，关闭当前弹层并重载表格
				$.ajax({
					url: '/AIFollowUp/FollowUpTemplates/Save',
					type: "post",
					data: { 'node': data.field },
					datatype: 'json',
					success: function (data) {
						if (data.okMsg) {
							layer.msg(data.okMsg);
							layer.close(windowsIndex);//关闭弹出层
							table.reload('tablelist'); //重载表格
						}
						else {
							layer.msg(data.errorMsg);
						}
						layer.close(indes);
					}, error: function (res) {
						layer.msg("加载统计信息错误：" + res.responseText);
						layer.close(indes);
					}
				});
				return false;
			});

			//监听提交
			form.on('submit(Detailsubmit)', function (data) {
				var indes = layer.load(1);
				data.field.templateDetailName = $("#TemplateDetailName").val();
				data.field.methods = $("#Methods").val();
				data.field.followupPoint = $("#FollowupPoint").val();
				data.field.templateId = $("#TemplateId").val();
				data.field.delayDay = $("#DelayDay").val();
				data.field.description = $("#Description").val();
				data.field.orderby = $("#Orderby").val();
				data.field.remark = $("#DetailRemark").val();
				data.field.id = $("#DetailId").val();
				if (data.field.templateDetailName == '') { layer.msg("模板详情名称不能为空！"); layer.close(indes); return false; }
				//提交 Ajax 成功后，关闭当前弹层并重载表格
				$.ajax({
					url: '/AIFollowUp/FollowUpTemplates/DetailSave',
					type: "post",
					data: { 'node': data.field },
					datatype: 'json',
					success: function (data) {
						if (data.okMsg) {
							layer.msg(data.okMsg);
							layer.close(windowsIndex);//关闭弹出层
							table.reload('detaillist'); //重载表格
						}
						else {
							layer.msg(data.errorMsg);
						}
						layer.close(indes);
					}, error: function (res) {
						layer.msg("加载统计信息错误：" + res.responseText);
						layer.close(indes);
					}
				});
				return false;
			});

			function setTableH() {
				var winH = $(window).height();
				var navH = $(".layui-tab-brief").height();
				var searchH = $(".search_wrap").height();
				var editAreaH = $(".edit_area").height();
				var tableH = winH - (navH + searchH + editAreaH) - 55 + "px";
				console.log(tableH)
				$(".table_wrap").css("height", tableH);

			};
			setTableH();
			$(window).resize(function () {
				setTableH()
			});
		});
	</script>
</body>

</html>
