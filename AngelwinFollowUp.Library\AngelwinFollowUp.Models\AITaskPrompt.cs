﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AngelwinFollowUp.Models
{
    /// <summary>
    /// AI任务提示词(任务执行、任务总结、特征变量提取)三个节点的提示词
    /// </summary>
    public partial class AITaskPrompt
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        [StringLength(100)]
        public string PropmtPoint { get; set; } = null!; //提示词使用点三者之一(任务执行、任务总结、特征变量提取)
        [StringLength(100)]
        public string PropmtName { get; set; } = null!;  //(提示词简称)
        [StringLength(20)]
        public string ModelName { get; set; } = null!;//(大模型)---通用、 GLM4、deepseek、qwq

        [ForeignKey("HospitalDept")]
        public int? HospitalDeptId { get; set; } //所属机构
        public virtual HospitalDept HospitalDept { get; set; } = null!;

        [ForeignKey("AITaskType")]
        public int AITaskTypeId { get; set; } //外键，关联 AITaskType 表
        public virtual AITaskType AITaskType { get; set; } = null!;

        [StringLength(4000)]
        public string Prompt { get; set; } = null!;//(提示词）
        [StringLength(2000)]
        public string Note { get; set; } = null!;//（说明)

        [DefaultValue(true)]
        public bool IsUsed { get; set; }//(是否可用）
        [StringLength(50)]
        public string CreateUser { get; set; } = null!;
        public DateTime CreatedTime { get; set; }



    }
}
