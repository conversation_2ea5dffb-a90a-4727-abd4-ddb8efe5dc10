{"Files": [{"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\HZRecorder.js", "PackagePath": "staticwebassets\\KeDaXunFei\\HZRecorder.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\HmacSHA1.js", "PackagePath": "staticwebassets\\KeDaXunFei\\HmacSHA1.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\base64.js", "PackagePath": "staticwebassets\\KeDaXunFei\\base64.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\crypto-js.js", "PackagePath": "staticwebassets\\KeDaXunFei\\crypto-js.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\dist\\index.cjs.js", "PackagePath": "staticwebassets\\KeDaXunFei\\dist\\index.cjs.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\dist\\index.d.ts", "PackagePath": "staticwebassets\\KeDaXunFei\\dist\\index.d.ts"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\dist\\index.esm.js", "PackagePath": "staticwebassets\\KeDaXunFei\\dist\\index.esm.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\dist\\index.umd.js", "PackagePath": "staticwebassets\\KeDaXunFei\\dist\\index.umd.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\dist\\processor.worker.js", "PackagePath": "staticwebassets\\KeDaXunFei\\dist\\processor.worker.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\dist\\processor.worklet.js", "PackagePath": "staticwebassets\\KeDaXunFei\\dist\\processor.worklet.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\enc-base64-min.js", "PackagePath": "staticwebassets\\KeDaXunFei\\enc-base64-min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\fast-xml-parser.min.js", "PackagePath": "staticwebassets\\KeDaXunFei\\fast-xml-parser.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\hmac-sha256.js", "PackagePath": "staticwebassets\\KeDaXunFei\\hmac-sha256.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\md5.js", "PackagePath": "staticwebassets\\KeDaXunFei\\md5.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\rtasr\\XunFeiRecord.js", "PackagePath": "staticwebassets\\KeDaXunFei\\rtasr\\XunFeiRecord.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\rtasr\\index.html", "PackagePath": "staticwebassets\\KeDaXunFei\\rtasr\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\rtasr\\xunfei_rtasr.js", "PackagePath": "staticwebassets\\KeDaXunFei\\rtasr\\xunfei_rtasr.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\tts-demo\\README.md", "PackagePath": "staticwebassets\\KeDaXunFei\\tts-demo\\README.md"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\tts-demo\\dist\\index.cjs.js", "PackagePath": "staticwebassets\\KeDaXunFei\\tts-demo\\dist\\index.cjs.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\tts-demo\\dist\\index.d.ts", "PackagePath": "staticwebassets\\KeDaXunFei\\tts-demo\\dist\\index.d.ts"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\tts-demo\\dist\\index.esm.js", "PackagePath": "staticwebassets\\KeDaXunFei\\tts-demo\\dist\\index.esm.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\tts-demo\\dist\\index.umd.js", "PackagePath": "staticwebassets\\KeDaXunFei\\tts-demo\\dist\\index.umd.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\tts-demo\\dist\\processor.worker.js", "PackagePath": "staticwebassets\\KeDaXunFei\\tts-demo\\dist\\processor.worker.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\tts-demo\\example\\base64.js", "PackagePath": "staticwebassets\\KeDaXunFei\\tts-demo\\example\\base64.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\tts-demo\\example\\crypto-js.js", "PackagePath": "staticwebassets\\KeDaXunFei\\tts-demo\\example\\crypto-js.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\tts-demo\\example\\tts\\index.html", "PackagePath": "staticwebassets\\KeDaXunFei\\tts-demo\\example\\tts\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\tts-demo\\example\\tts\\index.js", "PackagePath": "staticwebassets\\KeDaXunFei\\tts-demo\\example\\tts\\index.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\tts\\dist\\index.cjs.js", "PackagePath": "staticwebassets\\KeDaXunFei\\tts\\dist\\index.cjs.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\tts\\dist\\index.d.ts", "PackagePath": "staticwebassets\\KeDaXunFei\\tts\\dist\\index.d.ts"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\tts\\dist\\index.esm.js", "PackagePath": "staticwebassets\\KeDaXunFei\\tts\\dist\\index.esm.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\tts\\dist\\index.umd.js", "PackagePath": "staticwebassets\\KeDaXunFei\\tts\\dist\\index.umd.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\tts\\dist\\processor.worker.js", "PackagePath": "staticwebassets\\KeDaXunFei\\tts\\dist\\processor.worker.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\tts\\tts.js", "PackagePath": "staticwebassets\\KeDaXunFei\\tts\\tts.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\tts\\ttsModule.js", "PackagePath": "staticwebassets\\KeDaXunFei\\tts\\ttsModule.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\KeDaXunFei\\tts\\xunfei_tts.js", "PackagePath": "staticwebassets\\KeDaXunFei\\tts\\xunfei_tts.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\css\\chatGTPIndex.css", "PackagePath": "staticwebassets\\css\\chatGTPIndex.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\css\\editor.scss", "PackagePath": "staticwebassets\\css\\editor.scss"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\css\\fonticon.scss", "PackagePath": "staticwebassets\\css\\fonticon.scss"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\css\\jquery.magnify.css", "PackagePath": "staticwebassets\\css\\jquery.magnify.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\css\\knowledgeBase_style.css", "PackagePath": "staticwebassets\\css\\knowledgeBase_style.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\css\\login2_style.css", "PackagePath": "staticwebassets\\css\\login2_style.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\css\\loginstyle.css", "PackagePath": "staticwebassets\\css\\loginstyle.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\css\\paddemo_style.css", "PackagePath": "staticwebassets\\css\\paddemo_style.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\css\\simditor.css", "PackagePath": "staticwebassets\\css\\simditor.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\css\\simditor.scss", "PackagePath": "staticwebassets\\css\\simditor.scss"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\css\\site.css", "PackagePath": "staticwebassets\\css\\site.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\css\\welcome_style.css", "PackagePath": "staticwebassets\\css\\welcome_style.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\echarts\\echarts.min.js", "PackagePath": "staticwebassets\\echarts\\echarts.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\echarts\\theme\\dark.js", "PackagePath": "staticwebassets\\echarts\\theme\\dark.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\echarts\\theme\\macarons.js", "PackagePath": "staticwebassets\\echarts\\theme\\macarons.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\echarts\\theme\\shine.js", "PackagePath": "staticwebassets\\echarts\\theme\\shine.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\echarts\\theme\\walden.js", "PackagePath": "staticwebassets\\echarts\\theme\\walden.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\favicon.ico", "PackagePath": "staticwebassets\\favicon.ico"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\AI_icon.png", "PackagePath": "staticwebassets\\images\\AI_icon.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\AI_icon2.png", "PackagePath": "staticwebassets\\images\\AI_icon2.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\13e1fa87369b49dfa61528cd24be0c54.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\13e1fa87369b49dfa61528cd24be0c54.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\13e1fa87369b49dfa61528cd24be0c54_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\13e1fa87369b49dfa61528cd24be0c54_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\2.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\2.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\3.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\3.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\4.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\4.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\7.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\7.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\banner.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\banner.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\098e046860924f7391cc417cb727851d.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\098e046860924f7391cc417cb727851d.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\098e046860924f7391cc417cb727851d_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\098e046860924f7391cc417cb727851d_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\13e1fa87369b49dfa61528cd24be0c54.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\13e1fa87369b49dfa61528cd24be0c54.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\13e1fa87369b49dfa61528cd24be0c54_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\13e1fa87369b49dfa61528cd24be0c54_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\1f39d81a132f41daab51aa8a250cd367.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\1f39d81a132f41daab51aa8a250cd367.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\1f39d81a132f41daab51aa8a250cd367_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\1f39d81a132f41daab51aa8a250cd367_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\2e154767c5a141db9246a011f514c63a.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\2e154767c5a141db9246a011f514c63a.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\2e154767c5a141db9246a011f514c63a_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\2e154767c5a141db9246a011f514c63a_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\371cb67bb9ca44779ad179574a44ee20.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\371cb67bb9ca44779ad179574a44ee20.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\371cb67bb9ca44779ad179574a44ee20_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\371cb67bb9ca44779ad179574a44ee20_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\4097cd8758b94b37b31090c750d4abd3.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\4097cd8758b94b37b31090c750d4abd3.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\4097cd8758b94b37b31090c750d4abd3_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\4097cd8758b94b37b31090c750d4abd3_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\4389f677387c428d85ca5d584f84bee3.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\4389f677387c428d85ca5d584f84bee3.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\4389f677387c428d85ca5d584f84bee3_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\4389f677387c428d85ca5d584f84bee3_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\54c315e2908b4cbe9dd4cff341f0b65f.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\54c315e2908b4cbe9dd4cff341f0b65f.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\54c315e2908b4cbe9dd4cff341f0b65f_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\54c315e2908b4cbe9dd4cff341f0b65f_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\60d31ee2739c4e7a941412b3dfb8226d.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\60d31ee2739c4e7a941412b3dfb8226d.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\60d31ee2739c4e7a941412b3dfb8226d_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\60d31ee2739c4e7a941412b3dfb8226d_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\65fa56c05b9e4ac1a99cf1efc8d9f840.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\65fa56c05b9e4ac1a99cf1efc8d9f840.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\65fa56c05b9e4ac1a99cf1efc8d9f840_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\65fa56c05b9e4ac1a99cf1efc8d9f840_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\6b715389681749b79466a254d0fa45ca.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\6b715389681749b79466a254d0fa45ca.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\6b715389681749b79466a254d0fa45ca_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\6b715389681749b79466a254d0fa45ca_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\6d0d362236064bf29898d156fbecbf21.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\6d0d362236064bf29898d156fbecbf21.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\6d0d362236064bf29898d156fbecbf21_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\6d0d362236064bf29898d156fbecbf21_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\76506f9dee5e40e69bba449519dd1c53.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\76506f9dee5e40e69bba449519dd1c53.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\76506f9dee5e40e69bba449519dd1c53_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\76506f9dee5e40e69bba449519dd1c53_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\7704d11da1c141c0aa9742e1b17e5704.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\7704d11da1c141c0aa9742e1b17e5704.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\7704d11da1c141c0aa9742e1b17e5704_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\7704d11da1c141c0aa9742e1b17e5704_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\7e1411c454954655b523a3ab79757e31.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\7e1411c454954655b523a3ab79757e31.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\7e1411c454954655b523a3ab79757e31_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\7e1411c454954655b523a3ab79757e31_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\871fe5bee2ef4b37994eb4b64fde75e4.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\871fe5bee2ef4b37994eb4b64fde75e4.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\871fe5bee2ef4b37994eb4b64fde75e4_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\871fe5bee2ef4b37994eb4b64fde75e4_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\8a3ce8051fd441c1a5344ae025037859.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\8a3ce8051fd441c1a5344ae025037859.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\8a3ce8051fd441c1a5344ae025037859_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\8a3ce8051fd441c1a5344ae025037859_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\9180f2c1a6af406091fa7623583a2805.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\9180f2c1a6af406091fa7623583a2805.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\9180f2c1a6af406091fa7623583a2805_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\9180f2c1a6af406091fa7623583a2805_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\922a8859322e4a1f98e396e00be6b4f8.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\922a8859322e4a1f98e396e00be6b4f8.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\922a8859322e4a1f98e396e00be6b4f8_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\922a8859322e4a1f98e396e00be6b4f8_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\a79c1970fb4a4d29a1a865db532296c9.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\a79c1970fb4a4d29a1a865db532296c9.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\a79c1970fb4a4d29a1a865db532296c9_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\a79c1970fb4a4d29a1a865db532296c9_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\b08facb58c6b4a448313ef76e422defc.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\b08facb58c6b4a448313ef76e422defc.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\b08facb58c6b4a448313ef76e422defc_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\b08facb58c6b4a448313ef76e422defc_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\baba446e1bf2453c98d89c4c41202581.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\baba446e1bf2453c98d89c4c41202581.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\baba446e1bf2453c98d89c4c41202581_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\baba446e1bf2453c98d89c4c41202581_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\d6c9fe0a17f84d2f8393727fb78e4448.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\d6c9fe0a17f84d2f8393727fb78e4448.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\d6c9fe0a17f84d2f8393727fb78e4448_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\d6c9fe0a17f84d2f8393727fb78e4448_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\df276538f18e4d0e90295c2e1b513b36.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\df276538f18e4d0e90295c2e1b513b36.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\df276538f18e4d0e90295c2e1b513b36_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\df276538f18e4d0e90295c2e1b513b36_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\ebc75c9b87c643bfa4ce0f2ca0051608.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\ebc75c9b87c643bfa4ce0f2ca0051608.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\ebc75c9b87c643bfa4ce0f2ca0051608_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\ebc75c9b87c643bfa4ce0f2ca0051608_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\fefc866ddb2741e982b8ca21028aeafb.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\fefc866ddb2741e982b8ca21028aeafb.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CRFormsPad\\eCRF_img\\fefc866ddb2741e982b8ca21028aeafb_t.jpg", "PackagePath": "staticwebassets\\images\\CRFormsPad\\eCRF_img\\fefc866ddb2741e982b8ca21028aeafb_t.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\CT2.pdf", "PackagePath": "staticwebassets\\images\\CT2.pdf"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\JY1.jpg", "PackagePath": "staticwebassets\\images\\JY1.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\JY2.jpg", "PackagePath": "staticwebassets\\images\\JY2.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\base_icon.png", "PackagePath": "staticwebassets\\images\\base_icon.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\caduceus.png", "PackagePath": "staticwebassets\\images\\caduceus.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\chat_icon_A1.png", "PackagePath": "staticwebassets\\images\\chat_icon_A1.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\chat_icon_A2.png", "PackagePath": "staticwebassets\\images\\chat_icon_A2.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\chat_icon_AGPT.jpg", "PackagePath": "staticwebassets\\images\\chat_icon_AGPT.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\chat_icon_Q.jpg", "PackagePath": "staticwebassets\\images\\chat_icon_Q.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\color.png", "PackagePath": "staticwebassets\\images\\color.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\crfdemo1.jpg", "PackagePath": "staticwebassets\\images\\crfdemo1.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\crfdemo2.jpg", "PackagePath": "staticwebassets\\images\\crfdemo2.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\dataNull.png", "PackagePath": "staticwebassets\\images\\dataNull.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\hospitallogo1.png", "PackagePath": "staticwebassets\\images\\hospitallogo1.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\icon_1.png", "PackagePath": "staticwebassets\\images\\icon_1.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\icon_2.png", "PackagePath": "staticwebassets\\images\\icon_2.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\icon_3.png", "PackagePath": "staticwebassets\\images\\icon_3.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\img_icon.png", "PackagePath": "staticwebassets\\images\\img_icon.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\img_icon1.png", "PackagePath": "staticwebassets\\images\\img_icon1.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\banner1.jpg", "PackagePath": "staticwebassets\\images\\ipadindex\\banner1.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\banner2.jpg", "PackagePath": "staticwebassets\\images\\ipadindex\\banner2.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\banner2_pic.png", "PackagePath": "staticwebassets\\images\\ipadindex\\banner2_pic.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\banner3.jpg", "PackagePath": "staticwebassets\\images\\ipadindex\\banner3.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\banner5.jpg", "PackagePath": "staticwebassets\\images\\ipadindex\\banner5.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\ipad_icon1.jpg", "PackagePath": "staticwebassets\\images\\ipadindex\\ipad_icon1.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\ipad_icon10.png", "PackagePath": "staticwebassets\\images\\ipadindex\\ipad_icon10.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\ipad_icon11.png", "PackagePath": "staticwebassets\\images\\ipadindex\\ipad_icon11.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\ipad_icon12.png", "PackagePath": "staticwebassets\\images\\ipadindex\\ipad_icon12.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\ipad_icon2.jpg", "PackagePath": "staticwebassets\\images\\ipadindex\\ipad_icon2.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\ipad_icon3.jpg", "PackagePath": "staticwebassets\\images\\ipadindex\\ipad_icon3.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\ipad_icon4.jpg", "PackagePath": "staticwebassets\\images\\ipadindex\\ipad_icon4.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\ipad_icon5.jpg", "PackagePath": "staticwebassets\\images\\ipadindex\\ipad_icon5.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\ipad_icon6.jpg", "PackagePath": "staticwebassets\\images\\ipadindex\\ipad_icon6.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\ipad_icon7.jpg", "PackagePath": "staticwebassets\\images\\ipadindex\\ipad_icon7.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\ipad_icon8.jpg", "PackagePath": "staticwebassets\\images\\ipadindex\\ipad_icon8.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\ipad_icon9.jpg", "PackagePath": "staticwebassets\\images\\ipadindex\\ipad_icon9.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\next_icon.png", "PackagePath": "staticwebassets\\images\\ipadindex\\next_icon.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\ipadindex\\prev_icon.png", "PackagePath": "staticwebassets\\images\\ipadindex\\prev_icon.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\left_img.png", "PackagePath": "staticwebassets\\images\\left_img.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\login2.png", "PackagePath": "staticwebassets\\images\\login2.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\login2\\bg2.jpg", "PackagePath": "staticwebassets\\images\\login2\\bg2.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\login_bg.jpg", "PackagePath": "staticwebassets\\images\\login_bg.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\logo.png", "PackagePath": "staticwebassets\\images\\logo.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\logo1.png", "PackagePath": "staticwebassets\\images\\logo1.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\mp4\\video1.png", "PackagePath": "staticwebassets\\images\\mp4\\video1.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\mp4\\video2.png", "PackagePath": "staticwebassets\\images\\mp4\\video2.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\mp4\\video3.png", "PackagePath": "staticwebassets\\images\\mp4\\video3.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\mp4\\妇产新生儿科-病历转写2.mp4", "PackagePath": "staticwebassets\\images\\mp4\\妇产新生儿科-病历转写2.mp4"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\mp4\\特征变量提取.mp4", "PackagePath": "staticwebassets\\images\\mp4\\特征变量提取.mp4"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\mp4\\随访转写.mp4", "PackagePath": "staticwebassets\\images\\mp4\\随访转写.mp4"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\not_open.png", "PackagePath": "staticwebassets\\images\\not_open.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\pdf_icon.png", "PackagePath": "staticwebassets\\images\\pdf_icon.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\pdf_icon1.png", "PackagePath": "staticwebassets\\images\\pdf_icon1.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\user_pic.png", "PackagePath": "staticwebassets\\images\\user_pic.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\welcome\\bg1.jpg", "PackagePath": "staticwebassets\\images\\welcome\\bg1.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\welcome\\bg_camera.png", "PackagePath": "staticwebassets\\images\\welcome\\bg_camera.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\welcome\\bg_consultation.png", "PackagePath": "staticwebassets\\images\\welcome\\bg_consultation.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\welcome\\bg_fav.png", "PackagePath": "staticwebassets\\images\\welcome\\bg_fav.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\welcome\\bg_home.png", "PackagePath": "staticwebassets\\images\\welcome\\bg_home.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\welcome\\bg_shop.png", "PackagePath": "staticwebassets\\images\\welcome\\bg_shop.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\welcome\\bg_user.png", "PackagePath": "staticwebassets\\images\\welcome\\bg_user.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\welcome\\camera.png", "PackagePath": "staticwebassets\\images\\welcome\\camera.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\welcome\\consultation.png", "PackagePath": "staticwebassets\\images\\welcome\\consultation.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\welcome\\fav.png", "PackagePath": "staticwebassets\\images\\welcome\\fav.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\welcome\\home.png", "PackagePath": "staticwebassets\\images\\welcome\\home.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\welcome\\line.png", "PackagePath": "staticwebassets\\images\\welcome\\line.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\welcome\\shop.png", "PackagePath": "staticwebassets\\images\\welcome\\shop.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\welcome\\title.png", "PackagePath": "staticwebassets\\images\\welcome\\title.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\welcome\\tjbg_01.jpg", "PackagePath": "staticwebassets\\images\\welcome\\tjbg_01.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\welcome\\tjbg_02.jpg", "PackagePath": "staticwebassets\\images\\welcome\\tjbg_02.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\welcome\\tjbg_03.jpg", "PackagePath": "staticwebassets\\images\\welcome\\tjbg_03.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\welcome\\user.png", "PackagePath": "staticwebassets\\images\\welcome\\user.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\zjpBG.png", "PackagePath": "staticwebassets\\images\\zjpBG.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\images\\zjpcrf.jpg", "PackagePath": "staticwebassets\\images\\zjpcrf.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\js\\AngelwinForm.js", "PackagePath": "staticwebassets\\js\\AngelwinForm.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\js\\common.js", "PackagePath": "staticwebassets\\js\\common.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\js\\hotkeys.js", "PackagePath": "staticwebassets\\js\\hotkeys.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\js\\hotkeys.min.js", "PackagePath": "staticwebassets\\js\\hotkeys.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\js\\jquery-1.10.2.min.js", "PackagePath": "staticwebassets\\js\\jquery-1.10.2.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\js\\jquery-3.5.1.min.js", "PackagePath": "staticwebassets\\js\\jquery-3.5.1.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\js\\jquery.easing.1.3.js", "PackagePath": "staticwebassets\\js\\jquery.easing.1.3.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\js\\jquery.magnify.js", "PackagePath": "staticwebassets\\js\\jquery.magnify.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\js\\jquery.min.js", "PackagePath": "staticwebassets\\js\\jquery.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\js\\marked.min.js", "PackagePath": "staticwebassets\\js\\marked.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\js\\module.js", "PackagePath": "staticwebassets\\js\\module.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\js\\module.min.js", "PackagePath": "staticwebassets\\js\\module.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\js\\simditor.js", "PackagePath": "staticwebassets\\js\\simditor.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\js\\simditor.min.js", "PackagePath": "staticwebassets\\js\\simditor.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\js\\site.js", "PackagePath": "staticwebassets\\js\\site.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\js\\uploader.js", "PackagePath": "staticwebassets\\js\\uploader.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\js\\uploader.min.js", "PackagePath": "staticwebassets\\js\\uploader.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\config.js", "PackagePath": "staticwebassets\\layuiadmin\\config.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\Package.json", "PackagePath": "staticwebassets\\layuiadmin\\json\\Package.json"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\User.json", "PackagePath": "staticwebassets\\layuiadmin\\json\\User.json"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\console\\prograss.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\console\\prograss.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\console\\top-card.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\console\\top-card.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\console\\top-search.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\console\\top-search.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\content\\comment.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\content\\comment.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\content\\list.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\content\\list.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\content\\tags.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\content\\tags.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\forum\\list.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\forum\\list.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\forum\\replys.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\forum\\replys.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\layer\\photos.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\layer\\photos.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\layim\\getList.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\layim\\getList.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\layim\\getMembers.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\layim\\getMembers.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\mall\\order.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\mall\\order.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\menu.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\menu.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\message\\all.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\message\\all.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\message\\detail.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\message\\detail.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\message\\direct.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\message\\direct.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\message\\new.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\message\\new.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\message\\notice.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\message\\notice.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\schedule.json", "PackagePath": "staticwebassets\\layuiadmin\\json\\schedule.json"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\table\\Reportfile.json", "PackagePath": "staticwebassets\\layuiadmin\\json\\table\\Reportfile.json"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\table\\demo.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\table\\demo.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\table\\demo2.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\table\\demo2.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\table\\demo3.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\table\\demo3.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\table\\prolis.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\table\\prolis.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\table\\user.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\table\\user.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\table\\user30.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\table\\user30.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\table\\userHC.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\table\\userHC.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\upload\\demo.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\upload\\demo.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\user\\forget.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\user\\forget.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\user\\login.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\user\\login.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\user\\logout.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\user\\logout.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\user\\reg.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\user\\reg.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\user\\resetpass.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\user\\resetpass.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\user\\session.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\user\\session.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\user\\sms.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\user\\sms.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\useradmin\\mangadmin.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\useradmin\\mangadmin.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\useradmin\\role.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\useradmin\\role.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\useradmin\\webuser.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\useradmin\\webuser.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\workorder\\demo.js", "PackagePath": "staticwebassets\\layuiadmin\\json\\workorder\\demo.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\json\\说明.txt", "PackagePath": "staticwebassets\\layuiadmin\\json\\说明.txt"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\appointment.css", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\appointment.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\base.css", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\base.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\layui.css", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\layui.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\code.css", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\code.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\laydate\\default\\font\\iconfont.eot", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\laydate\\default\\font\\iconfont.eot"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\laydate\\default\\font\\iconfont.svg", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\laydate\\default\\font\\iconfont.svg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\laydate\\default\\font\\iconfont.ttf", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\laydate\\default\\font\\iconfont.ttf"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\laydate\\default\\font\\iconfont.woff", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\laydate\\default\\font\\iconfont.woff"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\laydate\\default\\laydate.css", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\laydate\\default\\laydate.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\layer\\default\\icon-ext.png", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\layer\\default\\icon-ext.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\layer\\default\\icon.png", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\layer\\default\\icon.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\layer\\default\\layer.css", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\layer\\default\\layer.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\layer\\default\\loading-0.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\layer\\default\\loading-0.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\layer\\default\\loading-1.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\layer\\default\\loading-1.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\layer\\default\\loading-2.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\layer\\default\\loading-2.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\layim\\html\\chatlog.html", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\layim\\html\\chatlog.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\layim\\html\\find.html", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\layim\\html\\find.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\layim\\html\\getmsg.json", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\layim\\html\\getmsg.json"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\layim\\html\\msgbox.html", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\layim\\html\\msgbox.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\layim\\layim.css", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\layim\\layim.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\layim\\skin\\1.jpg", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\layim\\skin\\1.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\layim\\skin\\2.jpg", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\layim\\skin\\2.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\layim\\skin\\3.jpg", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\layim\\skin\\3.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\layim\\skin\\4.jpg", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\layim\\skin\\4.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\layim\\skin\\5.jpg", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\layim\\skin\\5.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\layim\\skin\\logo.jpg", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\layim\\skin\\logo.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\css\\modules\\layim\\voice\\default.mp3", "PackagePath": "staticwebassets\\layuiadmin\\layui\\css\\modules\\layim\\voice\\default.mp3"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\font\\eyes_icon\\iconfont.css", "PackagePath": "staticwebassets\\layuiadmin\\layui\\font\\eyes_icon\\iconfont.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\font\\eyes_icon\\iconfont.ttf", "PackagePath": "staticwebassets\\layuiadmin\\layui\\font\\eyes_icon\\iconfont.ttf"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\font\\iconfont.eot", "PackagePath": "staticwebassets\\layuiadmin\\layui\\font\\iconfont.eot"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\font\\iconfont.svg", "PackagePath": "staticwebassets\\layuiadmin\\layui\\font\\iconfont.svg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\font\\iconfont.ttf", "PackagePath": "staticwebassets\\layuiadmin\\layui\\font\\iconfont.ttf"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\font\\iconfont.woff", "PackagePath": "staticwebassets\\layuiadmin\\layui\\font\\iconfont.woff"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\font\\iconfont.woff2", "PackagePath": "staticwebassets\\layuiadmin\\layui\\font\\iconfont.woff2"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\font\\web_font\\iconfont.css", "PackagePath": "staticwebassets\\layuiadmin\\layui\\font\\web_font\\iconfont.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\font\\web_font\\iconfont.ttf", "PackagePath": "staticwebassets\\layuiadmin\\layui\\font\\web_font\\iconfont.ttf"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\font\\web_font\\iconfont.woff2", "PackagePath": "staticwebassets\\layuiadmin\\layui\\font\\web_font\\iconfont.woff2"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\formSelects\\formSelects-v3.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\formSelects\\formSelects-v3.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\formSelects\\formSelects-v4.css", "PackagePath": "staticwebassets\\layuiadmin\\layui\\formSelects\\formSelects-v4.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\formSelects\\formSelects-v4.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\formSelects\\formSelects-v4.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\formSelects\\formSelects-v4.min.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\formSelects\\formSelects-v4.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\icon_font\\demo.css", "PackagePath": "staticwebassets\\layuiadmin\\layui\\icon_font\\demo.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\icon_font\\demo_index.html", "PackagePath": "staticwebassets\\layuiadmin\\layui\\icon_font\\demo_index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\icon_font\\iconfont.css", "PackagePath": "staticwebassets\\layuiadmin\\layui\\icon_font\\iconfont.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\icon_font\\iconfont.eot", "PackagePath": "staticwebassets\\layuiadmin\\layui\\icon_font\\iconfont.eot"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\icon_font\\iconfont.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\icon_font\\iconfont.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\icon_font\\iconfont.json", "PackagePath": "staticwebassets\\layuiadmin\\layui\\icon_font\\iconfont.json"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\icon_font\\iconfont.svg", "PackagePath": "staticwebassets\\layuiadmin\\layui\\icon_font\\iconfont.svg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\icon_font\\iconfont.ttf", "PackagePath": "staticwebassets\\layuiadmin\\layui\\icon_font\\iconfont.ttf"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\icon_font\\iconfont.woff", "PackagePath": "staticwebassets\\layuiadmin\\layui\\icon_font\\iconfont.woff"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\icon_font\\iconfont.woff2", "PackagePath": "staticwebassets\\layuiadmin\\layui\\icon_font\\iconfont.woff2"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\0.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\0.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\1.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\1.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\10.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\10.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\11.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\11.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\12.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\12.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\13.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\13.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\14.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\14.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\15.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\15.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\16.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\16.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\17.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\17.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\18.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\18.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\19.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\19.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\2.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\2.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\20.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\20.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\21.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\21.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\22.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\22.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\23.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\23.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\24.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\24.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\25.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\25.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\26.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\26.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\27.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\27.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\28.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\28.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\29.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\29.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\3.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\3.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\30.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\30.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\31.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\31.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\32.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\32.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\33.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\33.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\34.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\34.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\35.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\35.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\36.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\36.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\37.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\37.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\38.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\38.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\39.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\39.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\4.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\4.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\40.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\40.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\41.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\41.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\42.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\42.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\43.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\43.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\44.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\44.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\45.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\45.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\46.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\46.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\47.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\47.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\48.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\48.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\49.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\49.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\5.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\5.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\50.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\50.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\51.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\51.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\52.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\52.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\53.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\53.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\54.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\54.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\55.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\55.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\56.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\56.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\57.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\57.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\58.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\58.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\59.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\59.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\6.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\6.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\60.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\60.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\61.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\61.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\62.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\62.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\63.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\63.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\64.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\64.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\65.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\65.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\66.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\66.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\67.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\67.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\68.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\68.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\69.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\69.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\7.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\7.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\70.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\70.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\71.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\71.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\8.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\8.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\images\\face\\9.gif", "PackagePath": "staticwebassets\\layuiadmin\\layui\\images\\face\\9.gif"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\carousel.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\carousel.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\code.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\code.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\colorpicker.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\colorpicker.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\element.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\element.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\flow.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\flow.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\form.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\form.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\jquery.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\jquery.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\laydate.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\laydate.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\layedit.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\layedit.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\layer.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\layer.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\layim.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\layim.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\laypage.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\laypage.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\laytpl.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\laytpl.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\mobile.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\mobile.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\rate.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\rate.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\slider.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\slider.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\table.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\table.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\transfer.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\transfer.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\tree.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\tree.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\treeNew.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\treeNew.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\upload.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\upload.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\lay\\modules\\util.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\lay\\modules\\util.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\layui.all.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\layui.all.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\layui.js", "PackagePath": "staticwebassets\\layuiadmin\\layui\\layui.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\style\\admin.css", "PackagePath": "staticwebassets\\layuiadmin\\layui\\style\\admin.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\style\\login.css", "PackagePath": "staticwebassets\\layuiadmin\\layui\\style\\login.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\style\\res\\bg-none.jpg", "PackagePath": "staticwebassets\\layuiadmin\\layui\\style\\res\\bg-none.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\style\\res\\layui-logo.jpg", "PackagePath": "staticwebassets\\layuiadmin\\layui\\style\\res\\layui-logo.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\style\\res\\logo-black.png", "PackagePath": "staticwebassets\\layuiadmin\\layui\\style\\res\\logo-black.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\style\\res\\logo.png", "PackagePath": "staticwebassets\\layuiadmin\\layui\\style\\res\\logo.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\style\\res\\template\\character.jpg", "PackagePath": "staticwebassets\\layuiadmin\\layui\\style\\res\\template\\character.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\style\\res\\template\\huge.jpg", "PackagePath": "staticwebassets\\layuiadmin\\layui\\style\\res\\template\\huge.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\style\\res\\template\\portrait.png", "PackagePath": "staticwebassets\\layuiadmin\\layui\\style\\res\\template\\portrait.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layui\\style\\template.css", "PackagePath": "staticwebassets\\layuiadmin\\layui\\style\\template.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layuiextend\\treeTable.js", "PackagePath": "staticwebassets\\layuiadmin\\layuiextend\\treeTable.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\layuiextend\\xm-select.js", "PackagePath": "staticwebassets\\layuiadmin\\layuiextend\\xm-select.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\lib\\admin.js", "PackagePath": "staticwebassets\\layuiadmin\\lib\\admin.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\lib\\extend\\echarts.js", "PackagePath": "staticwebassets\\layuiadmin\\lib\\extend\\echarts.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\lib\\extend\\echartsTheme.js", "PackagePath": "staticwebassets\\layuiadmin\\lib\\extend\\echartsTheme.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\lib\\index.js", "PackagePath": "staticwebassets\\layuiadmin\\lib\\index.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\lib\\view.js", "PackagePath": "staticwebassets\\layuiadmin\\lib\\view.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\modules\\common.js", "PackagePath": "staticwebassets\\layuiadmin\\modules\\common.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\modules\\console.js", "PackagePath": "staticwebassets\\layuiadmin\\modules\\console.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\modules\\contlist.js", "PackagePath": "staticwebassets\\layuiadmin\\modules\\contlist.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\modules\\forum.js", "PackagePath": "staticwebassets\\layuiadmin\\modules\\forum.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\modules\\iconPicker.js", "PackagePath": "staticwebassets\\layuiadmin\\modules\\iconPicker.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\modules\\im.js", "PackagePath": "staticwebassets\\layuiadmin\\modules\\im.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\modules\\message.js", "PackagePath": "staticwebassets\\layuiadmin\\modules\\message.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\modules\\sample.js", "PackagePath": "staticwebassets\\layuiadmin\\modules\\sample.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\modules\\senior.js", "PackagePath": "staticwebassets\\layuiadmin\\modules\\senior.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\modules\\set.js", "PackagePath": "staticwebassets\\layuiadmin\\modules\\set.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\modules\\treeTable.js", "PackagePath": "staticwebassets\\layuiadmin\\modules\\treeTable.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\modules\\user.js", "PackagePath": "staticwebassets\\layuiadmin\\modules\\user.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\modules\\useradmin.js", "PackagePath": "staticwebassets\\layuiadmin\\modules\\useradmin.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\modules\\workorder.js", "PackagePath": "staticwebassets\\layuiadmin\\modules\\workorder.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\style\\admin.css", "PackagePath": "staticwebassets\\layuiadmin\\style\\admin.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\style\\login.css", "PackagePath": "staticwebassets\\layuiadmin\\style\\login.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\style\\res\\bg-none.jpg", "PackagePath": "staticwebassets\\layuiadmin\\style\\res\\bg-none.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\style\\res\\layui-logo.jpg", "PackagePath": "staticwebassets\\layuiadmin\\style\\res\\layui-logo.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\style\\res\\logo-black.png", "PackagePath": "staticwebassets\\layuiadmin\\style\\res\\logo-black.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\style\\res\\logo.png", "PackagePath": "staticwebassets\\layuiadmin\\style\\res\\logo.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\style\\res\\template\\character.jpg", "PackagePath": "staticwebassets\\layuiadmin\\style\\res\\template\\character.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\style\\res\\template\\huge.jpg", "PackagePath": "staticwebassets\\layuiadmin\\style\\res\\template\\huge.jpg"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\style\\res\\template\\portrait.png", "PackagePath": "staticwebassets\\layuiadmin\\style\\res\\template\\portrait.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\style\\template.css", "PackagePath": "staticwebassets\\layuiadmin\\style\\template.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\tpl\\layim\\demo.html", "PackagePath": "staticwebassets\\layuiadmin\\tpl\\layim\\demo.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\tpl\\system\\about.html", "PackagePath": "staticwebassets\\layuiadmin\\tpl\\system\\about.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\tpl\\system\\get.html", "PackagePath": "staticwebassets\\layuiadmin\\tpl\\system\\get.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\tpl\\system\\more.html", "PackagePath": "staticwebassets\\layuiadmin\\tpl\\system\\more.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\tpl\\system\\theme.html", "PackagePath": "staticwebassets\\layuiadmin\\tpl\\system\\theme.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\tpl\\system\\说明.txt", "PackagePath": "staticwebassets\\layuiadmin\\tpl\\system\\说明.txt"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\layuiadmin\\tpl\\说明.txt", "PackagePath": "staticwebassets\\layuiadmin\\tpl\\说明.txt"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\lib\\codemirror.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\lib\\codemirror.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\lib\\codemirror.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\lib\\codemirror.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\apl\\apl.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\apl\\apl.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\apl\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\apl\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\asciiarmor\\asciiarmor.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\asciiarmor\\asciiarmor.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\asciiarmor\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\asciiarmor\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\asn.1\\asn.1.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\asn.1\\asn.1.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\asn.1\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\asn.1\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\asterisk\\asterisk.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\asterisk\\asterisk.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\asterisk\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\asterisk\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\brainfuck\\brainfuck.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\brainfuck\\brainfuck.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\brainfuck\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\brainfuck\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\clike\\clike.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\clike\\clike.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\clike\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\clike\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\clike\\scala.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\clike\\scala.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\clike\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\clike\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\clojure\\clojure.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\clojure\\clojure.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\clojure\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\clojure\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\cmake\\cmake.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\cmake\\cmake.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\cmake\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\cmake\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\cobol\\cobol.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\cobol\\cobol.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\cobol\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\cobol\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\coffeescript\\coffeescript.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\coffeescript\\coffeescript.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\coffeescript\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\coffeescript\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\commonlisp\\commonlisp.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\commonlisp\\commonlisp.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\commonlisp\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\commonlisp\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\crystal\\crystal.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\crystal\\crystal.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\crystal\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\crystal\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\css\\css.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\css\\css.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\css\\gss.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\css\\gss.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\css\\gss_test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\css\\gss_test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\css\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\css\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\css\\less.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\css\\less.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\css\\less_test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\css\\less_test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\css\\scss.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\css\\scss.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\css\\scss_test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\css\\scss_test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\css\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\css\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\cypher\\cypher.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\cypher\\cypher.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\cypher\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\cypher\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\d\\d.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\d\\d.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\d\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\d\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\dart\\dart.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\dart\\dart.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\dart\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\dart\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\diff\\diff.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\diff\\diff.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\diff\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\diff\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\django\\django.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\django\\django.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\django\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\django\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\dockerfile\\dockerfile.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\dockerfile\\dockerfile.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\dockerfile\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\dockerfile\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\dtd\\dtd.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\dtd\\dtd.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\dtd\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\dtd\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\dylan\\dylan.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\dylan\\dylan.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\dylan\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\dylan\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\dylan\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\dylan\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\ebnf\\ebnf.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\ebnf\\ebnf.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\ebnf\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\ebnf\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\ecl\\ecl.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\ecl\\ecl.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\ecl\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\ecl\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\eiffel\\eiffel.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\eiffel\\eiffel.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\eiffel\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\eiffel\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\elm\\elm.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\elm\\elm.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\elm\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\elm\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\erlang\\erlang.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\erlang\\erlang.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\erlang\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\erlang\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\factor\\factor.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\factor\\factor.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\factor\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\factor\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\fcl\\fcl.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\fcl\\fcl.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\fcl\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\fcl\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\forth\\forth.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\forth\\forth.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\forth\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\forth\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\fortran\\fortran.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\fortran\\fortran.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\fortran\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\fortran\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\gas\\gas.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\gas\\gas.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\gas\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\gas\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\gfm\\gfm.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\gfm\\gfm.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\gfm\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\gfm\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\gfm\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\gfm\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\gherkin\\gherkin.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\gherkin\\gherkin.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\gherkin\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\gherkin\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\go\\go.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\go\\go.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\go\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\go\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\groovy\\groovy.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\groovy\\groovy.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\groovy\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\groovy\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\haml\\haml.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\haml\\haml.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\haml\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\haml\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\haml\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\haml\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\handlebars\\handlebars.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\handlebars\\handlebars.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\handlebars\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\handlebars\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\haskell-literate\\haskell-literate.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\haskell-literate\\haskell-literate.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\haskell-literate\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\haskell-literate\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\haskell\\haskell.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\haskell\\haskell.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\haskell\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\haskell\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\haxe\\haxe.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\haxe\\haxe.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\haxe\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\haxe\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\htmlembedded\\htmlembedded.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\htmlembedded\\htmlembedded.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\htmlembedded\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\htmlembedded\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\htmlmixed\\htmlmixed.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\htmlmixed\\htmlmixed.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\htmlmixed\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\htmlmixed\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\http\\http.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\http\\http.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\http\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\http\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\idl\\idl.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\idl\\idl.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\idl\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\idl\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\javascript\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\javascript\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\javascript\\javascript.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\javascript\\javascript.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\javascript\\json-ld.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\javascript\\json-ld.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\javascript\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\javascript\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\javascript\\typescript.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\javascript\\typescript.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\jinja2\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\jinja2\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\jinja2\\jinja2.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\jinja2\\jinja2.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\jsx\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\jsx\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\jsx\\jsx.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\jsx\\jsx.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\jsx\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\jsx\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\julia\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\julia\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\julia\\julia.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\julia\\julia.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\livescript\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\livescript\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\livescript\\livescript.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\livescript\\livescript.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\lua\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\lua\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\lua\\lua.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\lua\\lua.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\markdown\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\markdown\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\markdown\\markdown.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\markdown\\markdown.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\markdown\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\markdown\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\mathematica\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\mathematica\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\mathematica\\mathematica.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\mathematica\\mathematica.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\mbox\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\mbox\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\mbox\\mbox.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\mbox\\mbox.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\meta.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\meta.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\mirc\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\mirc\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\mirc\\mirc.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\mirc\\mirc.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\mllike\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\mllike\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\mllike\\mllike.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\mllike\\mllike.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\modelica\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\modelica\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\modelica\\modelica.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\modelica\\modelica.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\mscgen\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\mscgen\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\mscgen\\mscgen.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\mscgen\\mscgen.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\mscgen\\mscgen_test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\mscgen\\mscgen_test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\mscgen\\msgenny_test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\mscgen\\msgenny_test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\mscgen\\xu_test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\mscgen\\xu_test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\mumps\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\mumps\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\mumps\\mumps.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\mumps\\mumps.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\nginx\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\nginx\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\nginx\\nginx.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\nginx\\nginx.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\nsis\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\nsis\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\nsis\\nsis.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\nsis\\nsis.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\ntriples\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\ntriples\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\ntriples\\ntriples.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\ntriples\\ntriples.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\octave\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\octave\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\octave\\octave.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\octave\\octave.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\oz\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\oz\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\oz\\oz.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\oz\\oz.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\pascal\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\pascal\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\pascal\\pascal.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\pascal\\pascal.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\pegjs\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\pegjs\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\pegjs\\pegjs.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\pegjs\\pegjs.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\perl\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\perl\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\perl\\perl.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\perl\\perl.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\php\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\php\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\php\\php.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\php\\php.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\php\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\php\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\pig\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\pig\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\pig\\pig.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\pig\\pig.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\powershell\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\powershell\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\powershell\\powershell.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\powershell\\powershell.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\powershell\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\powershell\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\properties\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\properties\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\properties\\properties.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\properties\\properties.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\protobuf\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\protobuf\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\protobuf\\protobuf.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\protobuf\\protobuf.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\pug\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\pug\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\pug\\pug.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\pug\\pug.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\puppet\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\puppet\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\puppet\\puppet.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\puppet\\puppet.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\python\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\python\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\python\\python.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\python\\python.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\python\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\python\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\q\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\q\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\q\\q.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\q\\q.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\r\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\r\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\r\\r.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\r\\r.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\rpm\\changes\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\rpm\\changes\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\rpm\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\rpm\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\rpm\\rpm.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\rpm\\rpm.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\rst\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\rst\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\rst\\rst.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\rst\\rst.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\ruby\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\ruby\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\ruby\\ruby.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\ruby\\ruby.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\ruby\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\ruby\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\rust\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\rust\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\rust\\rust.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\rust\\rust.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\rust\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\rust\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\sas\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\sas\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\sas\\sas.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\sas\\sas.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\sass\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\sass\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\sass\\sass.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\sass\\sass.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\scheme\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\scheme\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\scheme\\scheme.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\scheme\\scheme.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\shell\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\shell\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\shell\\shell.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\shell\\shell.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\shell\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\shell\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\sieve\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\sieve\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\sieve\\sieve.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\sieve\\sieve.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\slim\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\slim\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\slim\\slim.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\slim\\slim.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\slim\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\slim\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\smalltalk\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\smalltalk\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\smalltalk\\smalltalk.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\smalltalk\\smalltalk.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\smarty\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\smarty\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\smarty\\smarty.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\smarty\\smarty.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\solr\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\solr\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\solr\\solr.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\solr\\solr.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\soy\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\soy\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\soy\\soy.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\soy\\soy.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\sparql\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\sparql\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\sparql\\sparql.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\sparql\\sparql.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\spreadsheet\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\spreadsheet\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\spreadsheet\\spreadsheet.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\spreadsheet\\spreadsheet.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\sql\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\sql\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\sql\\sql.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\sql\\sql.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\stex\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\stex\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\stex\\stex.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\stex\\stex.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\stex\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\stex\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\stylus\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\stylus\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\stylus\\stylus.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\stylus\\stylus.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\swift\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\swift\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\swift\\swift.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\swift\\swift.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\tcl\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\tcl\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\tcl\\tcl.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\tcl\\tcl.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\textile\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\textile\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\textile\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\textile\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\textile\\textile.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\textile\\textile.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\tiddlywiki\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\tiddlywiki\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\tiddlywiki\\tiddlywiki.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\tiddlywiki\\tiddlywiki.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\tiddlywiki\\tiddlywiki.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\tiddlywiki\\tiddlywiki.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\tiki\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\tiki\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\tiki\\tiki.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\tiki\\tiki.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\tiki\\tiki.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\tiki\\tiki.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\toml\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\toml\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\toml\\toml.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\toml\\toml.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\tornado\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\tornado\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\tornado\\tornado.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\tornado\\tornado.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\troff\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\troff\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\troff\\troff.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\troff\\troff.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\ttcn-cfg\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\ttcn-cfg\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\ttcn-cfg\\ttcn-cfg.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\ttcn-cfg\\ttcn-cfg.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\ttcn\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\ttcn\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\ttcn\\ttcn.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\ttcn\\ttcn.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\turtle\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\turtle\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\turtle\\turtle.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\turtle\\turtle.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\twig\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\twig\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\twig\\twig.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\twig\\twig.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\vb\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\vb\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\vb\\vb.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\vb\\vb.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\vbscript\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\vbscript\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\vbscript\\vbscript.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\vbscript\\vbscript.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\velocity\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\velocity\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\velocity\\velocity.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\velocity\\velocity.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\verilog\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\verilog\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\verilog\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\verilog\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\verilog\\verilog.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\verilog\\verilog.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\vhdl\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\vhdl\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\vhdl\\vhdl.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\vhdl\\vhdl.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\vue\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\vue\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\vue\\vue.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\vue\\vue.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\webidl\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\webidl\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\webidl\\webidl.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\webidl\\webidl.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\xml\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\xml\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\xml\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\xml\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\xml\\xml.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\xml\\xml.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\xquery\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\xquery\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\xquery\\test.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\xquery\\test.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\xquery\\xquery.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\xquery\\xquery.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\yacas\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\yacas\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\yacas\\yacas.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\yacas\\yacas.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\yaml-frontmatter\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\yaml-frontmatter\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\yaml-frontmatter\\yaml-frontmatter.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\yaml-frontmatter\\yaml-frontmatter.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\yaml\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\yaml\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\yaml\\yaml.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\yaml\\yaml.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\z80\\index.html", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\z80\\index.html"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\mode\\z80\\z80.js", "PackagePath": "staticwebassets\\lib\\CodeMirror\\mode\\z80\\z80.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\3024-day.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\3024-day.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\3024-night.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\3024-night.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\abcdef.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\abcdef.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\ambiance-mobile.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\ambiance-mobile.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\ambiance.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\ambiance.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\base16-dark.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\base16-dark.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\base16-light.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\base16-light.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\bespin.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\bespin.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\blackboard.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\blackboard.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\cobalt.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\cobalt.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\colorforth.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\colorforth.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\dracula.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\dracula.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\eclipse.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\eclipse.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\elegant.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\elegant.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\erlang-dark.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\erlang-dark.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\hopscotch.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\hopscotch.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\icecoder.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\icecoder.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\isotope.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\isotope.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\lesser-dark.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\lesser-dark.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\liquibyte.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\liquibyte.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\material.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\material.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\mbo.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\mbo.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\mdn-like.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\mdn-like.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\midnight.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\midnight.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\monokai.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\monokai.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\neat.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\neat.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\neo.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\neo.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\night.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\night.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\panda-syntax.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\panda-syntax.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\paraiso-dark.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\paraiso-dark.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\paraiso-light.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\paraiso-light.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\pastel-on-dark.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\pastel-on-dark.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\railscasts.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\railscasts.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\rubyblue.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\rubyblue.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\seti.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\seti.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\solarized.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\solarized.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\the-matrix.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\the-matrix.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\tomorrow-night-bright.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\tomorrow-night-bright.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\tomorrow-night-eighties.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\tomorrow-night-eighties.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\ttcn.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\ttcn.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\twilight.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\twilight.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\vibrant-ink.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\vibrant-ink.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\xq-dark.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\xq-dark.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\xq-light.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\xq-light.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\yeti.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\yeti.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\CodeMirror\\theme\\zenburn.css", "PackagePath": "staticwebassets\\lib\\CodeMirror\\theme\\zenburn.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\MuiHk\\css\\mui.min.css", "PackagePath": "staticwebassets\\lib\\MuiHk\\css\\mui.min.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\MuiHk\\js\\mui.min.js", "PackagePath": "staticwebassets\\lib\\MuiHk\\js\\mui.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\audio\\css\\audio_style.css", "PackagePath": "staticwebassets\\lib\\audio\\css\\audio_style.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\audio\\img\\audio_icon.psd", "PackagePath": "staticwebassets\\lib\\audio\\img\\audio_icon.psd"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\audio\\img\\iconloop.png", "PackagePath": "staticwebassets\\lib\\audio\\img\\iconloop.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\audioypbf\\css\\style.css", "PackagePath": "staticwebassets\\lib\\audioypbf\\css\\style.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\audioypbf\\js\\jquery-1.8.3.min.js", "PackagePath": "staticwebassets\\lib\\audioypbf\\js\\jquery-1.8.3.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\audioypbf\\js\\jweixin-1.0.0.js", "PackagePath": "staticwebassets\\lib\\audioypbf\\js\\jweixin-1.0.0.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\audioypbf\\mp3\\guanju.mp3", "PackagePath": "staticwebassets\\lib\\audioypbf\\mp3\\guanju.mp3"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\audioypbf\\mp3\\mov_bbb.mp4", "PackagePath": "staticwebassets\\lib\\audioypbf\\mp3\\mov_bbb.mp4"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\audioypbf\\mp3\\你有新短消息.mp3", "PackagePath": "staticwebassets\\lib\\audioypbf\\mp3\\你有新短消息.mp3"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\audioypbf\\mp3\\新的在线消息.mp3", "PackagePath": "staticwebassets\\lib\\audioypbf\\mp3\\新的在线消息.mp3"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\audioypbf\\mp3\\新的询价委托.mp3", "PackagePath": "staticwebassets\\lib\\audioypbf\\mp3\\新的询价委托.mp3"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\audioypbf\\mp3\\新的询价请回复.mp3", "PackagePath": "staticwebassets\\lib\\audioypbf\\mp3\\新的询价请回复.mp3"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\LICENSE", "PackagePath": "staticwebassets\\lib\\bootstrap"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\columnDrag\\column_drag.css", "PackagePath": "staticwebassets\\lib\\columnDrag\\column_drag.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\columnDrag\\column_drag.js", "PackagePath": "staticwebassets\\lib\\columnDrag\\column_drag.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\dialog\\handlebars.min.js", "PackagePath": "staticwebassets\\lib\\dialog\\handlebars.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\dialog\\list.min.js", "PackagePath": "staticwebassets\\lib\\dialog\\list.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\dialog\\script.js", "PackagePath": "staticwebassets\\lib\\dialog\\script.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\dialog\\style.css", "PackagePath": "staticwebassets\\lib\\dialog\\style.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\jquery-Steps\\css\\images\\pointes_blue.png", "PackagePath": "staticwebassets\\lib\\jquery-Steps\\css\\images\\pointes_blue.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\jquery-Steps\\css\\images\\pointes_green.png", "PackagePath": "staticwebassets\\lib\\jquery-Steps\\css\\images\\pointes_green.png"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\jquery-Steps\\css\\ystep.css", "PackagePath": "staticwebassets\\lib\\jquery-Steps\\css\\ystep.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\jquery-Steps\\js\\jquery.min.js", "PackagePath": "staticwebassets\\lib\\jquery-Steps\\js\\jquery.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\jquery-Steps\\js\\setStep.js", "PackagePath": "staticwebassets\\lib\\jquery-Steps\\js\\setStep.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\LICENSE.txt"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "PackagePath": "staticwebassets\\lib\\jquery-validation\\LICENSE.md"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\additional-methods.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\additional-methods.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\jquery.validate.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\jquery.validate.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "PackagePath": "staticwebassets\\lib\\jquery\\LICENSE.txt"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.min.map"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\liMarquee\\css\\liMarquee.css", "PackagePath": "staticwebassets\\lib\\liMarquee\\css\\liMarquee.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\liMarquee\\js\\jquery.liMarquee.js", "PackagePath": "staticwebassets\\lib\\liMarquee\\js\\jquery.liMarquee.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\mui-player\\css\\mui-player.min.css", "PackagePath": "staticwebassets\\lib\\mui-player\\css\\mui-player.min.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\mui-player\\js\\mui-player.min.js", "PackagePath": "staticwebassets\\lib\\mui-player\\js\\mui-player.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\perfect-scrollbar\\perfect-scrollbar.css", "PackagePath": "staticwebassets\\lib\\perfect-scrollbar\\perfect-scrollbar.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\perfect-scrollbar\\perfect-scrollbar.min.js", "PackagePath": "staticwebassets\\lib\\perfect-scrollbar\\perfect-scrollbar.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\swiper\\dist\\css\\swiper.css", "PackagePath": "staticwebassets\\lib\\swiper\\dist\\css\\swiper.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\swiper\\dist\\css\\swiper.min.css", "PackagePath": "staticwebassets\\lib\\swiper\\dist\\css\\swiper.min.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\swiper\\dist\\js\\maps\\swiper.jquery.min.js.map", "PackagePath": "staticwebassets\\lib\\swiper\\dist\\js\\maps\\swiper.jquery.min.js.map"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\swiper\\dist\\js\\maps\\swiper.jquery.umd.min.js.map", "PackagePath": "staticwebassets\\lib\\swiper\\dist\\js\\maps\\swiper.jquery.umd.min.js.map"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\swiper\\dist\\js\\maps\\swiper.min.js.map", "PackagePath": "staticwebassets\\lib\\swiper\\dist\\js\\maps\\swiper.min.js.map"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\swiper\\dist\\js\\swiper.jquery.js", "PackagePath": "staticwebassets\\lib\\swiper\\dist\\js\\swiper.jquery.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\swiper\\dist\\js\\swiper.jquery.min.js", "PackagePath": "staticwebassets\\lib\\swiper\\dist\\js\\swiper.jquery.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\swiper\\dist\\js\\swiper.jquery.umd.js", "PackagePath": "staticwebassets\\lib\\swiper\\dist\\js\\swiper.jquery.umd.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\swiper\\dist\\js\\swiper.jquery.umd.min.js", "PackagePath": "staticwebassets\\lib\\swiper\\dist\\js\\swiper.jquery.umd.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\swiper\\dist\\js\\swiper.js", "PackagePath": "staticwebassets\\lib\\swiper\\dist\\js\\swiper.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\swiper\\dist\\js\\swiper.min.js", "PackagePath": "staticwebassets\\lib\\swiper\\dist\\js\\swiper.min.js"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\touchslider\\touchslider.css", "PackagePath": "staticwebassets\\lib\\touchslider\\touchslider.css"}, {"Id": "D:\\work space\\project\\三部\\AI随访\\AngelwinFollowUp.Web\\wwwroot\\lib\\touchslider\\touchslider.js", "PackagePath": "staticwebassets\\lib\\touchslider\\touchslider.js"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.AngelwinFollowUp.Web.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.AngelwinFollowUp.Web.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.build.AngelwinFollowUp.Web.props", "PackagePath": "build\\AngelwinFollowUp.Web.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.buildMultiTargeting.AngelwinFollowUp.Web.props", "PackagePath": "buildMultiTargeting\\AngelwinFollowUp.Web.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.buildTransitive.AngelwinFollowUp.Web.props", "PackagePath": "buildTransitive\\AngelwinFollowUp.Web.props"}], "ElementsToRemove": []}