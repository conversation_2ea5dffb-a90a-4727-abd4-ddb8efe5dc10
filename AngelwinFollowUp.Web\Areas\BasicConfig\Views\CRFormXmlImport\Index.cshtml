﻿@{
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单字段导入管理</title>
    <link href="~/layuiadmin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/layuiadmin/style/admin.css" rel="stylesheet" />
    <style>
        html{
            background-color:#fff;
        }
        /* 定义表头样式 */
        .layui-table-header .layui-table-cell {
            font-weight: bold; /* 加粗 */
            font-size: 14px; /* 可选：调整字体大小以提升可读性 */
            color: #333; /* 可选：调整字体颜色 */
        }
    </style>
</head>
<body>
    <div class="window_wrap" id="form_window">
        <form class="layui-form" lay-filter="fm" id="fm" action="">
            <div class="layui-card-body" style="width:40%">
                <div class="layui-form-item">
                    <label class="layui-form-label">科研机构</label>
                    <div class="layui-input-block">
                        <div id="xmDeptsList2" ></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">CRF表单</label>
                    <div class="layui-input-block">
                        <div id="xmSelectFormList2" ></div>
                    </div>
                </div>
                @*<div class="layui-form-item">
                    <label class="layui-form-label">XML表名</label>
                    <div class="layui-input-block">
                        <input type="text" class="layui-input" id="hanaName" name="hanaName" placeholder="请输入HANA表名" />
                    </div>
                </div>*@
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button type="button" class="layui-btn" lay-submit lay-filter="submit">提交</button>
                    </div>
                </div>
            </div>
          
        </form>
    </div>

    <script src="~/js/jquery-3.5.1.min.js"></script>
    <script src="~/layuiadmin/layui/layui.js"></script>
    <script src="~/layuiadmin/layuiextend/xm-select.js"></script>
    <script src="~/js/common.js"></script>
    <script>
        layui.use(['element', 'layer', 'table', 'laydate', 'form'], function () {
            var element = layui.element
                , layer = layui.layer
                , table = layui.table//表格
                , laydate = layui.laydate
                , $ = layui.$
                , form = layui.form;

            $(document).ready(function () {

                GetDeptsTree();
                    $("#fm")[0].reset();
                    $('#Id').val(0);
                    xmDeptsList2.update({
                        disabled: false
                    });
                    xmSelectFormList2.update({
                        disabled: false
                    });
            });

            var xmDeptsList2 = xmSelect.render({
                el: '#xmDeptsList2',
                model: { label: { type: 'text' } },
                prop: {
                    name: 'title',
                    value: 'id',
                },
                minWidth: 200,
                radio: true,
                filterable: true,
                clickClose: true,
                //树
                tree: {
                    show: true,
                    //非严格模式
                    strict: false,
                    //默认展开节点
                    expandedKeys: [-1],
                },
                data: []
                , on: function (val) {
                    getFormList(val.arr[0].id, "");
                }
            });

            

            var xmSelectFormList2 = xmSelect.render({
                el: '#xmSelectFormList2',
                autoRow: true,
                radio: true,
                prop: {
                    name: 'Name',
                    value: 'Id',
                },
                filterable: true,
                clickClose: true,
                tips: '请选择CRF表单',
                on: function (data) {
                },
                done: function (res) {
                }
            })

            function GetDeptsTree() {
                $.ajax({
                    url: '/CommAPI/GetOrgsTreeList',
                    type: "post",
                    datatype: 'json',
                    success: function (result) {
                        //xmDeptsList.update({
                        //    data: result
                        //});
                        xmDeptsList2.update({
                            data: result
                        });
                        if (result[0].id) {
                            var arr = new Array();
                            arr.push(result[0].id);
                            //xmDeptsList.setValue(arr);
                            getFormList(result[0].id, "");
                        }
                    }, error: function () {
                        layer.msg("获取失败！");
                    }
                })
            };

            function getFormList(val, defaultValue) {
                $.ajax({
                    url: '/BasicConfig/CRFormXmlImport/GetFormList?deptId=' + val,
                    type: "get",
                    datatype: 'json',
                    success: function (result) {
                        xmSelectFormList2.update({
                            data: result.data
                        });
                        if ($.trim(defaultValue) != '') {
                            var arr = new Array();
                            arr.push(defaultValue);
                            xmSelectFormList2.setValue(arr);
                        }
                    }, error: function () {
                        layer.msg("获取失败！");
                    }
                });
            }


            //监听提交
            form.on('submit(submit)', function (data) {
                var indes = layer.load(1);
                var loading = layer.load(1);
                var crformId = xmSelectFormList2.getValue('valueStr');
                //var formName = xmSelectFormList2.getValue('nameStr');
                if (crformId == null) {
                    layer.msg("请选择表单");
                    return false;
                }
                $.ajax({
                    url: "/BasicConfig/CRFormXmlImport/CheckExists",
                    type: 'post',
                    datatype: 'json',
                    data: {
                        'crformId': crformId
                    },
                    success: function (result) {
                        if (result.data > 0) {
                            var conf = layer.confirm('目前此表单有【' + result.data + '】条数据，确定要覆盖吗', {
                                btn: ['确定', '取消'],
                                resize: false
                            }, function () {
                                layer.close(conf);
                                $.ajax({
                                    url: "/BasicConfig/CRFormXmlImport/Create",
                                    type: "post",
                                    data: { 'crformId': crformId},
                                    datatype: 'json',
                                    success: function (data) {
                                        if (data.okMsg) {
                                            layer.msg(data.okMsg);
                                        }
                                        else {
                                            layer.msg(data.errorMsg);
                                        }
                                        layer.close(loading);
                                    }, error: function (res) {
                                        layer.msg("加载统计信息错误：" + res.responseText);
                                        layer.close(loading);
                                    }
                                });
                            }, function () {
                                layer.msg("取消");
                                layer.close(loading);
                            })
                        }
                        else {
                            $.ajax({
                                url: "/BasicConfig/CRFormXmlImport/Create",
                                type: "post",
                                data: { 'crformId': crformId},
                                datatype: 'json',
                                success: function (data) {
                                    if (data.okMsg) {
                                        layer.msg(data.okMsg);
                                    }
                                    else {
                                        layer.msg(data.errorMsg);
                                    }
                                    layer.close(loading);
                                }, error: function (res) {
                                    layer.msg("加载统计信息错误：" + res.responseText);
                                    layer.close(loading);
                                }
                            });
                        }
                    }
                });
                return false;
            });
        });
    </script>
</body>
</html>
