﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Unity\CommAPIController.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="EPPlus" Version="7.7.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="SharpToken" Version="2.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AngelwinFollowUp.Library\AngelwinFollowUp.Library.csproj" />
    <ProjectReference Include="..\Common.Tools\Common.Tools.csproj" />  </ItemGroup>

  <ItemGroup>
    <Folder Include="Areas\BasicConfig\Controllers\" />
    <Folder Include="Areas\BasicConfig\Views\" />
  </ItemGroup>

</Project>
