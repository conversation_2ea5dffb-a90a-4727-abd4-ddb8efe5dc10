﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AngelwinFollowUp.Library.Migrations
{
    /// <inheritdoc />
    public partial class V250514_01 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "FormDatas",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CRFormId = table.Column<int>(type: "int", nullable: true),
                    CRFJsonValue = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AIExtractJsonValue = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreateUserName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    CreatedTime = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FormDatas", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "FormDatas");
        }
    }
}
