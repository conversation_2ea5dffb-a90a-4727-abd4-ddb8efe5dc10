﻿using AngelwinFollowUp.Models;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AngelwinFollowUp.Models
{
    public partial class FollowupPlan
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [ForeignKey("ResearchPatient")]
        public int PatientId { get; set; } // 患者Id

        public virtual ResearchPatient ResearchPatient { get; set; } = null!;

        [ForeignKey("FollowupTemplate")]
        public int TemplateId { get; set; } // 随访模版明细Id
        public virtual FollowupTemplate FollowupTemplate { get; set; } = null!;

        [ForeignKey("FollowupTemplateDetail")]
        public int TemplateDetailId { get; set; } // 随访模版明细Id
        public virtual FollowupTemplateDetail FollowupTemplateDetail { get; set; } = null!;

        public required string Description { get; set; } = string.Empty; // 随访内容

        [DefaultValue(0)]
        public int Status { get; set; } // 随访状态(0:待随访，-1:失访；1:已随访)

        [MaxLength(2000)]
        public string? Remark { get; set; } // 备注

        public DateTime? StatusTime { get; set; }  // 随(失)访日期

        [MaxLength(100)]
        public string CreateUserName { get; set; } = string.Empty;  // 创建人

        public DateTime CreatedTime { get; set; } = System.DateTime.Now; // 创建时间
    }
}