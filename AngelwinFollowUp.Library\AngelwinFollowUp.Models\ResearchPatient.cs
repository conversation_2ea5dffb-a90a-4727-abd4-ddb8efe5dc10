﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AngelwinFollowUp.Models;

namespace AngelwinFollowUp.Models
{
    public partial class ResearchPatient
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public  string PatientId { get; set; } = string.Empty;
        [Required]
        [MaxLength(50)]
        public  string PatientSource { get; set; } = string.Empty;
        [MaxLength(30)]
        public string? BRKH { get; set; }  //卡号
        [MaxLength(30)]
        public string? BLH { get; set; } //住院号
        [Required]
        [MaxLength(50)]
        public  string PatientName { get; set; } = string.Empty;
        [MaxLength(50)]
        public string? IDCardNo { get; set; } 
        [MaxLength(30)]
        public string? Telephone { get; set; } = string.Empty;
        [MaxLength(10)]
        public string Sex { get; set; } = string.Empty;

        [MaxLength(200)]
        public string ICDCode { get; set; } = string.Empty;// 疾病ICD-10编码

        [MaxLength(200)]
        public string Diagnosis { get; set; } = string.Empty;// 诊断

        [ForeignKey("HospitalDept")]
        public int? HospitalDeptId { get; set; }
        public virtual HospitalDept HospitalDept { get; set; } = null!;

        public int Age { get; set; }  //年龄
        public DateTime? BrithDay { get; set; }
        public string CreateUserName { get; set; } = string.Empty;
        public DateTime CreatedTime { get; set; } = System.DateTime.Now;

        public ICollection<FollowupPlan> FollowupPlans { get; } = new List<FollowupPlan>();
        public ICollection<FollowupRecord> FollowupRecords { get; } = new List<FollowupRecord>();
    }
}
