﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;

namespace AngelwinFollowUp.Models
{
    /// <summary>
    /// AI任务CRF数据提取表 addbyzolf 20250709
    /// </summary>
    public partial class AITaskCRFData
    {

        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }//主键

        [ForeignKey("ResearchPatient")]
        public int PatientId { get; set; } // 患者Id
        public virtual ResearchPatient ResearchPatient { get; set; } = null!;

        [ForeignKey("AITaskType")]
        public int AITaskTypeId { get; set; } // 外键，关联 AITaskType 表
        public virtual AITaskType AITaskType { get; set; } = null!;

        [ForeignKey("FollowupRecord")]
        public int FollowupRecordId { get; set; } // 外键，关联 FollowupRecord 表
        public virtual FollowupRecord FollowupRecord { get; set; } = null!;

        [MaxLength(100)]
        public string FollowupRecordDetailIds { get; set; } = null!; //任务明细表Id，多个,分割；

        [Required]
        [MaxLength(50)]
        public string CRFormId { get; set; } = null!;//CRF表单Id
        [Required]
        [MaxLength(200)]
        public string FormName { get; set; } = null!;//表单名称
        public string CRFJsonValue { get; set; } = null!;//结构数据
        public string CRFTextData { get; set; } = null!;//文本数据
        public string AIExtractJsonValue { get; set; } = null!;//AI提取值

        [DefaultValue(0)]
        public int TotalField { get; set; }//需要填充单元格数
        [DefaultValue(0)]
        public int FillField { get; set; }//已经填充单元格数
        [MaxLength(2000)]
        public string Remark { get; set; } = null!;//备注
        [MaxLength(50)]
        public string CreateUserName { get; set; } = null!;//创建人
        public DateTime CreatedTime { get; set; }//创建时间

    }
}
