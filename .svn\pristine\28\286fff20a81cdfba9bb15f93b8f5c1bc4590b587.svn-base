﻿using AngelwinFollowUp.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using System.Linq;

namespace AngelwinFollowUp.Models
{
    public class AngelwinFollowUpDbContext : IdentityDbContext<UserInfo, RoleInfo, int>
    {
        public AngelwinFollowUpDbContext(DbContextOptions<AngelwinFollowUpDbContext> options)
            : base(options)
        {
        }
        public virtual DbSet<Menu> Menus { get; set; }
        public virtual DbSet<RoleMenu> RoleMenus { get; set; }
        public virtual DbSet<Log> Logs { get; set; }
        public virtual DbSet<LoginLog> LoginLogs { get; set; }
        public virtual DbSet<WebConfig> WebConfigs { get; set; }
        public virtual DbSet<FollowupTemplate> FollowupTemplates { get; set; }
        public virtual DbSet<FollowupTemplateDetail> FollowupTemplateDetails { get; set; }
        public virtual DbSet<FollowupPlan> FollowupPlans { get; set; }
        public virtual DbSet<FollowupRecord> FollowupRecords { get; set; }
        public virtual DbSet<FollowupRecordDetail> FollowupRecordDetails { get; set; }
        public virtual DbSet<HospitalDept> HospitalDepts { get; set; }

        public virtual DbSet<ResearchPatient> ResearchPatients { get; set; }

        public virtual DbSet<FormData> FormDatas { get; set; }

        public virtual DbSet<AITaskType> AITaskTypes { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            ///组合主键
            modelBuilder.Entity<RoleMenu>()
               .HasKey(o => new { o.RoleInfoId, o.MenuId });

            modelBuilder.Entity<UserInfo>().HasMany<IdentityUserRole<int>>((UserInfo e) => e.Roles)
               .WithOne().HasForeignKey((IdentityUserRole<int> e) => e.UserId).IsRequired(true);

            //modelBuilder.Entity<RoleInfo>().HasMany<IdentityUserRole<int>>((RoleInfo e) => e.Users)
            //    .WithOne().HasForeignKey((IdentityUserRole<int> e) => e.RoleId).IsRequired(true);

            modelBuilder.Entity<UserInfo>(entity =>
            {
                entity.HasMany<IdentityUserRole<int>>((UserInfo e) => e.Roles)
                      .WithOne().HasForeignKey((IdentityUserRole<int> e) => e.UserId).IsRequired(true);
            });

            modelBuilder.Entity<RoleInfo>(entity =>
            {
                entity.HasMany<IdentityUserRole<int>>((RoleInfo e) => e.Users)
                      .WithOne().HasForeignKey((IdentityUserRole<int> e) => e.RoleId).IsRequired(true);
                entity.HasMany(r => r.Users)
                      .WithOne()
                      .HasForeignKey(ur => ur.RoleId)
                      .IsRequired();

                entity.HasMany(r => r.Menus)
                      .WithOne(rm => rm.RoleInfo)
                      .HasForeignKey(rm => rm.RoleInfoId)
                      .IsRequired();
            });

            modelBuilder.Entity<WebConfig>().HasIndex(e => new { e.WebKey }).IsUnique();

            modelBuilder.Entity<FollowupPlan>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");
            modelBuilder.Entity<FollowupRecord>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");
            modelBuilder.Entity<FollowupRecordDetail>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");
            modelBuilder.Entity<FollowupTemplate>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");
            modelBuilder.Entity<FollowupTemplateDetail>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");
            modelBuilder.Entity<ResearchPatient>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");
            modelBuilder.Entity<WebConfig>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");
            modelBuilder.Entity<FormData>().Property("CreatedTime").HasDefaultValueSql("GETDATE()");

            foreach (IMutableForeignKey item in modelBuilder.Model.GetEntityTypes().SelectMany(e => e.GetForeignKeys()))
            {
                item.DeleteBehavior = DeleteBehavior.Restrict;
            }

        }
    }
}
