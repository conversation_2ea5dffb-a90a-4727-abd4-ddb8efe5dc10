﻿@{
    //ViewData["Title"] = "Home Page";
    Layout = null;
}

<!DOCTYPE html>
<html lang="zh">

<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="full-screen" content="yes">
	<meta name="x5-fullscreen" content="true">
	<title>工作台</title>
	<link rel="stylesheet" href="~/layuiadmin/layui/css/layui.css">
	<style>
		.pag10{
			padding:10px;

		}
		.row_wrap{
			display:flex;
			flex-direction:row;
			align-content:center;
			
		}

		.space_between{
			justify-content: space-between;
		}
        .item_warp {
            background-color: #fff;
            border-radius: 6px;
			overflow:hidden;
        }
        .item_warp p {
			padding: 8px;
		}
		.info_wrap span{
			display: inline-block;
			white-space: nowrap; 
		}

	



		/* 强制日历容器撑满 */
		#ID-laydate-cell-render {
			height: 100%;
		}
		#ID-laydate-cell-render .layui-laydate {
			width: 100% !important;
			height: 100% !important;
			margin: 0 !important;
		}
		#ID-laydate-cell-render .layui-laydate-content {
			height: calc(100% - 40px) !important;
		}
		#ID-laydate-cell-render .laydate-body-table {
			width: 100% !important;
			height: 100% !important;
			table-layout: fixed !important; 
		}
		#ID-laydate-cell-render .laydate-body-table tr {
			height: calc(100% / 6) !important; 
		}
		#ID-laydate-cell-render .laydate-body-table td {
			height: 100% !important;
			padding: 10px !important;
			vertical-align: middle !important;
		}
		.layui-laydate-main{
			width:100% !important;
		}
		.layui-laydate-content table{
			width: 100% !important;
		}
		/* 强制日历容器撑满 */
		.follow_data .layui-card-header{
			height:30px;
			line-height:30px;
		}

		.right_top{height:38%;margin-bottom:16px;}
		.right-bottom{height:62%;}



		.calculate_conrent{
			padding: 13% 20px;
		}

		.calculate h3{
			padding-top:30px;
			font-size:50px;
		}
		.bg1{
			background: url('/images/welcome/tjbg_01.jpg');
		}
		.bg2 {
			background: url('/images/welcome/tjbg_02.jpg');
		}
		.bg3{
			background: url('/images/welcome/tjbg_03.jpg');
		}

		.calculate {
			width: 97%;
			height: 220px;
			background-repeat: no-repeat;
			background-size: cover;
			color: #fff;
			margin: 0 5px;
			border-radius: 6px;
		}

		.bold_font{
			margin-right:20px;
			font-weight:bold;
			font-size:16px;
			white-space: nowrap;
		}

		.info_wrap .bold_font{
			margin-right: 0;
		}
		.layui-form-item .layui-input-inline{
			width:auto !important;
			min-width:100px;
		}
	</style>


</head>
<body class="layui-layout-body">
	<div id="LAY_app">
		<div class="layui-row">
			<div class="layui-col-xs3">
				<div class="pag10">
					<div class="row_wrap item_warp info_wrap" style="margin-bottom:10px;">
						<p><span>院区：</span><span class="bold_font">高新院区</span></p>
						<p><span>科室：</span><span class="bold_font">神经内科</span></p>
						<p><span>管理员：</span><span class="bold_font">护士</span></p>
					</div>
					<div class="item_warp follow_data ">
						<div class="layui-card">
							<div class="layui-card-header bold_font">今日随访</div>
							<div class="layui-card-body">
								<div class="row_wrap space_between"><p>计划随访</p><p class="itemval">2223</p></div>
								<div class="row_wrap space_between"><p>已随访</p><p class="itemval">20</p></div>
								<div class="row_wrap space_between"><p>未随访</p><p class="itemval">2203</p></div>
							</div>
						</div>
                        <div class="pag10">
							<div class="layui-inline" id="ID-laydate-cell-render" style="width:100%;height:100%"></div>
						</div>
						<div class="layui-card">
							<div class="layui-card-header bold_font">2024年12月15日 周三</div>
							<div class="layui-card-body">
								<div class="row_wrap space_between"><p>计划随访</p><p class="itemval">2223</p></div>
								<div class="row_wrap space_between"><p>已随访</p><p class="itemval">20</p></div>
								<div class="row_wrap space_between"><p>未随访</p><p class="itemval">2203</p></div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-xs9">
				<div class="right_wrap pag10">
					<div class="right_top">
						<div class="layui-card">
							<div class="layui-card-header bold_font">随访统计</div>
							<div class="layui-card-body">
								<div class="layui-row">
									<div class="layui-col-xs4 ">
										<div class="calculate bg1">
											<div class="calculate_conrent">
												<p>本月随访数</p>
												<h3>2223</h3>
											</div>
										</div>
									</div>
										<div class="layui-col-xs4">
										<div class="calculate bg2">
											<div class="calculate_conrent">
												<p>年度随访数</p>
												<h3>4200</h3>
											</div>
								
										</div>
									</div>
									<div class="layui-col-xs4">
										<div class="calculate bg3">
											<div class="calculate_conrent">
												<p>随访完成率</p>
												<h3>80%</h3>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="right_bottom layui-form">
						<div class="layui-row">
							<div class="layui-col-xs8">
								<div class="layui-card">
									<div class="layui-card-header row_wrap space_between">
										<p class="bold_font">患者统计</p>
										<div class="layui-form-item">
											<div class="layui-input-inline">
												<select name="activitySummary_date" lay-filter="activitySummary_date">
													<option value="0" selected>最近7天</option>
													<option value="1">本月</option>
												</select>
											</div>
										</div>
									</div>
									<div class="layui-card-body">
										<div class="canvas_wrap">
											<div class="canvas" style="width: 100%;height:100%;" id="activitySummary"></div>
										</div>
						 			</div> 
						 		</div> 
							</div>
							<div class="layui-col-xs4">
								<div class="layui-card">
									<div class="layui-card-header row_wrap space_between">
										<p class="bold_font">随访进度</p>
										<div class="layui-form-item">
											<div class="layui-input-inline">
												<select name="FollowProgress_dete" lay-filter="FollowProgress_dete">
													<option value="0" >最近7天</option>
													<option value="1" selected>本月</option>
												</select>
											</div>
										</div>
									</div>
									<div class="layui-card-body">
										 <div class="canvas_wrap">
											<div class="canvas" style="width: 100%;height:100%;" id="FollowProgress"></div>
										 </div>
						 			</div> 
						 		</div> 
							</div>
						</div>
					
					</div>
				</div>
			</div>
		</div>
	</div>


	<script type="text/javascript" src="~/js/jquery-3.5.1.min.js"></script>
	<script type="text/javascript" src="~/layuiadmin/layui/layui.js"></script>
	<script type="text/javascript" src="~/echarts/echarts.min.js"></script>
	<script type="text/javascript" src="~/echarts/theme/walden.js"></script>

	<script>
        layui.use(['jquery', 'layer', 'tree', 'form', 'element', 'upload', 'laytpl', 'table','laydate'], function () {
			var layer = layui.layer,
				$ = layui.$,
				form = layui.form,
                laydate = layui.laydate;

			let laydateInstance = null;


			//日历控件
			function initCalendar() {
				laydateInstance =  laydate.render({
						elem: '#ID-laydate-cell-render',
						position: 'static',
						isPreview: false,
						btns: ['clear', 'now', 'confirm'],
						theme: 'lunar',
						autoConfirm: false,
						ready: function (date) {
							if (!this._previewEl) {
							  var key = this.elem.attr('lay-key');
							  var panelEl = $('#layui-laydate' + key);
							  this._previewEl = panelEl.find('.layui-laydate-preview');
							  this.cellRender(date);
							}
						},
						change: function(value, date) {
							this.cellRender(date);
						},
						onNow: function(value, date) {
							this.cellRender(date);
						}
				});
			}
			layui.use('laydate', initCalendar);
			//日历控件


			 //患者列表的高度
			function setRightH() {
				var winH = $(window).height();
				var info_wrapH = $(".info_wrap").height();
				var follow_dataH = winH - info_wrapH-40 +"px";
				$(".follow_data").css("height",follow_dataH);

				var canvasH = winH/2-15 +"px";
				$(".canvas_wrap").css("height",canvasH);

			}
			setRightH();

			$(window).resize(function () {
				setRightH();
			});





			//饼图
		function showFollowProgress(res) {
			console.log(res); // 调试输出

			var myChart = echarts.init(document.getElementById('FollowProgress'), 'walden');

			option = {
				tooltip: {
					trigger: 'item'
				},
				legend: {
					top: '1%',
					left: 'center'
				},
				toolbox: {
					feature: {
					  saveAsImage: {}
					}
				},
				series: [{
					type: 'pie',
					  radius: ['30%', '60%'],
					  center: ['50%', '55%'],
					  avoidLabelOverlap: true,
					  padAngle:10,
					  itemStyle: {
							borderRadius: 10
					  },
					  label: {
							show: true,
							position: 'outside', 
							formatter: '{b}' 
					  },
					  emphasis: {
							label: {
							  show: true,
							  fontSize: 15
							}
					  },
					  labelLine: {
							show: true, 
							length: 15, 
							length2: 10,
							lineStyle: {
							  color: '#68729A',
							  width: 1, 
							  type: 'solid'
							}
					  },
					  data: res
				}]
			};

			myChart.setOption(option);
		}

		//折线图
		function activitySummary(res) {
		  var myChart = echarts.init(document.getElementById('activitySummary'), 'walden');
			var xAxisData = [];
			var seriesData = {};
			// 遍历数据，填充 xAxisData 和 seriesData
			res.forEach(function (item) {
				// 如果 xAxisData 中还没有该项时间，则添加
				if (xAxisData.indexOf(item.Time) === -1) {
					xAxisData.push(item.Time);
				}
				// 如果 seriesData 中还没有该项 OperationType，则初始化
				if (!seriesData[item.OperationType]) {
					seriesData[item.OperationType] = [];
				}
				// 添加对应时间的数据点
				seriesData[item.OperationType][xAxisData.indexOf(item.Time)] = item.Count;
			});

			// 构建 series 数组
			var series = [];
			Object.keys(seriesData).forEach(function (operationType) {
				series.push({
					name: operationType,
					type: 'line',
					stack: 'Total',
					data: seriesData[operationType]
				});
			});

			option = {
				  title: {
					text: ''
				  },
				  tooltip: {
					trigger: 'axis'
				  },
				  legend: {
					data: ['新增患者', '随访完成', '待随访']
				  },
				  grid: {
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				  },
				  toolbox: {
					feature: {
					  saveAsImage: {}
					}
				  },
				  xAxis: {
					type: 'category',
					boundaryGap: false,
					data: ['10-01', '10-02', '10-03', '10-04', '10-05', '10-06', '10-07']
				  },
				  yAxis: {
					type: 'value'
				},
				series:res
			};

			myChart.setOption(option);
		}
		
		
		
		
		
		
		
		
	




		// 测试数据
		var mockData1 = [
		  { value: 1048, name: '已完成' },
		  { value: 735, name: '进行中' },
		  { value: 580, name: '待开始' },
		  { value: 484, name: '已超时' }
		];

		//随访进度
		showFollowProgress(mockData1);

		// 测试数据
		var mockData2 = [
			{
			  name: '新增患者',
			  type: 'line',
			  stack: 'Total',
			  data: [120, 132, 101, 134, 90, 230, 210]
			},
			{
			  name: '随访完成',
			  type: 'line',
			  stack: 'Total',
			  data: [220, 182, 191, 234, 290, 330, 310]
			},
			{
			  name: '待随访',
			  type: 'line',
			  stack: 'Total',
			  data: [150, 232, 201, 154, 190, 330, 410]
			}

		  ]

		//患者统计
		activitySummary(mockData2)









		});
	</script>





</body>
</html>