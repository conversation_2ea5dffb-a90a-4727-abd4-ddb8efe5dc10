//const TTS_APPID = "8740d028";
//const TTS_API_SECRET = "YzI3NjU2MWQ4Y2YwMDNhN2ZkN2MxYjMx";
//const TTS_API_KEY = "14af5e16c0b2cca34b1f84b3e5383a88";

let IsEnd = 1;
let tts_xh = ""; //序号
let tts_btnStatus = "UNDEFINED";
let voiceTextSend = "";  //流式返回已发送文字
let voiceTextAll = "";//流式返回所有文字
const audioPlayer = new AudioPlayer("../../KeDaXunFei/tts/dist");


function tts_changeBtnStatus(tts_status) {
    tts_btnStatus = tts_status;
    var $btn = $("#audio_" + tts_xh);
    console.log($btn);
    var isPlay = $btn.find(".audio_area").hasClass("playing");
    console.log(tts_status + "，播放状态：" + isPlay);
    if (tts_status === "UNDEFINED") {
        //btnControl.innerText = "立即合成";
        if (isPlay) {
            $btn.find(".audio_area").removeClass("playing");
        } else {
            $btn.find(".audio_area").addClass("playing");
        }
    } else if (tts_status === "CONNECTING") {
        //btnControl.innerText = "正在合成";
        if (isPlay) {
            $btn.find(".audio_area").removeClass("playing");
        } else {
            $btn.find(".audio_area").addClass("playing");
        }
    } else if (tts_status === "PLAY") {
        //btnControl.innerText = "停止播放";
        $(".audio_area").removeClass("playing");
        $btn.find(".audio_area").addClass("playing");
    } else if (tts_status === "STOP") {
        //btnControl.innerText = "重新播放";
        if (isPlay) {
            $btn.find(".audio_area").removeClass("playing");
        }
    }
}

audioPlayer.onPlay = () => {
    tts_changeBtnStatus("PLAY");
};
audioPlayer.onStop = (audioDatas) => {
    //console.log(audioDatas);
    tts_btnStatus === "PLAY" && tts_changeBtnStatus("STOP");
    IsEnd = 1;
};
function tts_getWebSocketUrl(apiKey, apiSecret) {
    var url = "ws://tts-api.xfyun.cn/v2/tts";
    var host = location.host;
    var date = new Date().toGMTString();
    var algorithm = "hmac-sha256";
    var headers = "host date request-line";
    var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/tts HTTP/1.1`;
    var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
    var signature = CryptoJS.enc.Base64.stringify(signatureSha);
    var authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
    var authorization = btoa(authorizationOrigin);
    url = `${url}?authorization=${authorization}&date=${date}&host=${host}`;
    return url;
}
function encodeText(text, type) {
    if (type === "unicode") {
        let buf = new ArrayBuffer(text.length * 4);
        let bufView = new Uint16Array(buf);
        for (let i = 0, strlen = text.length; i < strlen; i++) {
            bufView[i] = text.charCodeAt(i);
        }
        let binary = "";
        let bytes = new Uint8Array(buf);
        let len = bytes.byteLength;
        for (let i = 0; i < len; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return window.btoa(binary);
    } else {
        return Base64.encode(text);
    }
}

let ttsWS;
function tts_connectWebSocket(text) {
    IsEnd = 0;
    const url = tts_getWebSocketUrl(TTS_API_KEY, TTS_API_SECRET);
    if ("WebSocket" in window) {
        ttsWS = new WebSocket(url);
    } else if ("MozWebSocket" in window) {
        ttsWS = new MozWebSocket(url);
    } else {
        alert("浏览器不支持WebSocket");
        return;
    }
    tts_changeBtnStatus("CONNECTING");
    ttsWS.onopen = (e) => {
        IsEnd = 0;
        audioPlayer.start({
            autoPlay: true,
            sampleRate: 16000,
            resumePlayDuration: 1000
        });
        tts_changeBtnStatus("PLAY");
        if (text === "") {
            text = $("#content_" + tts_xh).html().trim() || "请输入您要合成的文本";
        }
        console.log("#Send:" + text);
        var tte = "UTF8";//document.getElementById("tte").checked ? "unicode" : "UTF8";
        var params = {
            common: {
                app_id: TTS_APPID,
            },
            business: {
                aue: "raw",
                auf: "audio/L16;rate=16000",
                vcn: "x4_lingxiaowan_en",
                speed: +65,
                volume: +50,
                pitch: +50,
                bgs: 1,
                tte,
            },
            data: {
                status: 2,
                text: encodeText(text, tte),
            },
        };
        ttsWS.send(JSON.stringify(params));
    };
    ttsWS.onmessage = (e) => {
        let jsonData = JSON.parse(e.data);
        // 合成失败
        if (jsonData.code !== 0) {
            console.error(jsonData);
            tts_changeBtnStatus("UNDEFINED");
            return;
        }
        audioPlayer.postMessage({
            type: "base64",
            data: jsonData.data.audio,
            isLastData: jsonData.data.status === 2,
        });
        if (jsonData.code === 0 && jsonData.data.status === 2) {
            ttsWS.close();
        }
    };
    ttsWS.onerror = (e) => {
        console.error(e);
    };
    ttsWS.onclose = (e) => {
        // console.log(e);
    };
}
function RadioPlay(xh, text, stream = 0) {
    console.log(xh + ":" + tts_xh + ":" + tts_btnStatus);
    //xh != tts_xh：序号不一样是重启player
    //stream=1:流式响应时重启player
    //xh == tts_xh && xh.indexOf("new") >= 0  最后一次流式结束后，立即点击最后一次一条语音时，重启player
    if (xh != tts_xh || (xh == tts_xh && xh.indexOf("new") >= 0 && tts_btnStatus != "PLAY") || stream === 1) {
        tts_xh = xh;
        tts_btnStatus = "UNDEFINED";
        ttsWS?.close();
        audioPlayer.reset();
    }

    if (tts_btnStatus === "UNDEFINED") {
        // 开始合成
        this.tts_connectWebSocket(text);
    } else if (tts_btnStatus === "CONNECTING") {
        // 停止合成
        tts_changeBtnStatus("UNDEFINED");
        ttsWS?.close();
        audioPlayer.reset();
        return;
    } else if (tts_btnStatus === "PLAY") {
        audioPlayer.stop();
    } else if (tts_btnStatus === "STOP") {
        audioPlayer.play();
    }
}

let timer;
function RadioPlayStream(xh) {
    CloseAudio();
    CloseTimer(); // 清除旧的定时器
    var i = 0;
    // 保存 xh 的值到局部变量,避免闭包问题
    const localXh = xh;
    // 设置一个定时作业，每2000毫秒（2秒）执行一次
    timer = setInterval(function () {
        //voiceTextAll、voiceTextSend
        if (IsEnd == 1) {
            // 对 voiceTextSend 进行正则表达式转义
            var escapedVoiceTextSend = escapeRegExp(voiceTextSend);

            var text = voiceTextAll.replace(new RegExp(escapedVoiceTextSend, 'g'), '');

            voiceTextSend = voiceTextSend + text;
            // console.log(IsEnd + "A:" + voiceTextAll + "S:" + voiceTextSend + "N:" + text);
            if (text != "") {
                console.log("RadioPlayStream:" + localXh);
                RadioPlay(localXh, text, 1);
            }
            else {
                if (i != 0) {
                    CloseTimer();
                }
            }
        }
        i++;
    }, 1500);
}

// 转义正则表达式中的特殊字符
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& 表示匹配的整个字符串
}

function CloseAudio() {
    audioPlayer.stop();
}

function CloseTimer() {
    if (timer) {
        clearInterval(timer);
        timer = null;
    }
}


