﻿@{
	ViewBag.Title = "用户管理";
	Layout = null;
}

<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title> @ViewBag.SiteTitle</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
	<link href="~/layuiadmin/layui/css/layui.css" rel="stylesheet" />
	<link href="~/layuiadmin/style/admin.css" rel="stylesheet" />
	<link href="~/layuiadmin/layui/font/eyes_icon/iconfont.css" rel="stylesheet" />
	<script src="~/lib/jquery/dist/jquery.js"></script>
	<style>
		.layui-btn .layui-icon {
			margin: 0;
		}

		.flex-row {
			display: flex;
			flex-direction: row;
		}

		.row_between {
			justify-content: space-between;
		}

		.search_wrap {
			background-color: #fff;
			display: flex;
			flex-direction: row;
			padding: 10px 10px 10px 0;
			border-bottom: 10px solid #eee;
		}

		.pagebtn {
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			text-align: center;
		}

		/* 卡片样式 */
		.patient_item {
			padding: 10px;
			border: 1px solid #eee;
			border-radius: 4px;
			margin: 15px;
		}

		.flex-column {
			display: flex;
			flex-direction: column
		}

		.layui-inline {
			margin-left: 10px;
		}

		.content-inline {
			display: flex;
			flex-direction: row;
		}

		.info_left {
			flex: 1;
		}

		.patient_pic {
			width: 100px;
			height: 100px;
			text-align: center;
		}

			.patient_pic img {
				width: 100%;
				height: 100%;
			}

		.patient_btn_1 {
			text-align: center;
		}

		.layui-input, .layui-textarea{
			display: block;
			width: 90%;
			padding-left: 10px;
		}

			.patient_btn_1 .layui-btn {
				margin-left: 0;
			}

		.patient_btn_2 {
			padding-top: 10px;
			text-align: center;
			width: 100%;
		}

			.patient_btn_2 .layui-btn {
				width: 30%;
			}

		.user_name {
			line-height: 30px;
			font-size: 18px;
			font-weight: bold;
		}

			.user_name .layui-btn {
				border-radius: 15px;
				margin-left: 15px;
			}

		.user_value {
			color: #000;
		}

		.sex {
			font-size: 14px;
			font-weight: normal;
		}

		.sex0 {
			color: #FF5722;
		}

		.sex1 {
			color: #1E9FFF;
		}

		.table_wrap {
			overflow: hidden;
		}

		.tools_group {
			margin-top: 10px;
			padding-top: 10px;
			border-top: 1px solid #eee;
			justify-content: flex-end;
		}
	</style>

</head>
<body class="layui-layout-body">
	<div class="layui-card">
		<div class="search_wrap flex-row row_between">
			<form action="">
				<div class="layui-inline">
					<label class="layui-form-label">科室</label>
					<div class="layui-input-inline">
						<div id="xmDeptList" class="xm-select-demo" style="width:250px"></div>
					</div>
				</div>

				<div class="layui-input-inline" style="margin-left: 10px; width: 22vw;">
					<input type="text" class="layui-input" name="keyWord" placeholder="患者ID/姓名" id="keyWord">
				</div>
				<div class="layui-inline">
					<button type="button" class="layui-btn layui-btn-primary" id="Search">
						<i class="layui-icon layui-icon-search"></i>
					</button>
				</div>
			</form>
			<div class="layui-inline">
				<button class="layui-btn layui-btn-normal" id="addPatient">
					<i class="layui-icon layui-icon-addition"></i>
				</button>
			</div>
		</div>

		<div class="layui-row">
			<div class="layui-col-xs12">
				<div class="layui-card-body table_wrap">
					<table id="tablelist" lay-filter="tablelist"></table>
					<script type="text/html" id="tableBar">
						 <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="plan"><i class="layui-icon layui-icon-addition"></i>随访计划</a>
						 <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
						<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
					</script>
				</div>
			</div>
		</div>





	</div>
	<!--新增患者弹窗-->
	<div id="Patient_window" style="display:none;">
		<div class="add_patient ">
			<form class="layui-form" lay-filter="fm" id="fm" action="">
				<div class="layui-form-item" style="margin-top: 15px;">
					<label class="layui-form-label">科室名称</label>
					<div class="layui-input-block">
						<div id="xmDeptList2" class="xm-select-demo" style="width:250px"></div>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">患者ID</label>
					<div class="layui-input-block">
						<input type="text" name="Id" id="Id" style="display:none;" />
						<input type="text" name="PatientId" id="PatientId" lay-verify="required" placeholder="请输入" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">患者姓名</label>
					<div class="layui-input-block">
						<input type="text" name="PatientName" id="PatientName" lay-verify="required"  placeholder="请输入" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">患者来源</label>
					<div class="layui-input-block">
						<input type="radio" name="PatientSource" value="O" title="门诊">
						<input type="radio" name="PatientSource" value="I" title="住院">
						<input type="radio" name="PatientSource" value="P" title="体检">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">身份证号</label>
					<div class="layui-input-block">
						<input type="text" name="IDCardNo" id="IDCardNo" lay-verify="required" placeholder="请输入" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">性别</label>
					<div class="layui-input-block">
						<input type="radio" name="Sex" value="1" title="男">
						<input type="radio" name="Sex" value="0" title="女">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">年龄</label>
					<div class="layui-input-block">
						<input type="text" name="Age" id="Age" placeholder="请输入" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">出生日期</label>
					<div class="layui-input-block">
						<input name="BrithDay" id="BrithDay" lay-verify="required" placeholder="选择日期" type="text" class="layui-input">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">诊断</label>
					<div class="layui-input-block">
						<input type="text" name="Diagnosis" id="Diagnosis" placeholder="请输入" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">ICD代码</label>
					<div class="layui-input-block">
						<input type="text" name="ICDCode" id="ICDCode" placeholder="请输入" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">电话号码</label>
					<div class="layui-input-block">
					<input type="text" name="Telephone" id="Telephone" placeholder="请输入" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">住院号</label>
					<div class="layui-input-block">
						<input type="text" name="BLH" id="BLH" placeholder="请输入" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">卡号</label>
					<div class="layui-input-block">
						<input type="text" name="BRKH" id="BRKH" placeholder="请输入" autocomplete="off" class="layui-input">
					</div>
				</div>				
				<div class="layui-form-item">
					<div class="layui-input-block">
						<button type="button" class="layui-btn" lay-submit lay-filter="submit">提交</button>
						<button type="reset" class="layui-btn layui-btn-primary" id="btn_reset">重置</button>
					</div>
				</div>
			</form>
		</div>
	</div>

	<!--新增患者弹窗-->
	<div id="addPlan_window" style="display:none;">
		<div class="add_patient ">
			<form class="layui-form" lay-filter="fm" id="fm" action="">
				<div class="layui-form-item" style="margin-top: 15px;">
					<label class="layui-form-label">患者ID</label>
					<div class="layui-input-block">
						<input type="text" name="PlanId" id="PlanId" style="display:none;" />
						<input type="text" name="PId" id="PId" style="display:none;" />
						<input type="text" name="PlanPatientId" id="PlanPatientId" placeholder="请输入" autocomplete="off" class="layui-input" readonly>
					</div>
				</div>
				<div class="layui-form-item" style="margin-top: 15px;">
					<label class="layui-form-label">随访模板</label>
					<div class="layui-input-block">
						<div id="GetTempList" class="xm-select-demo" style="width:250px"></div>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">随访详情</label>
					<div class="layui-input-block">
						<div id="GetTempDetailList" class="xm-select-demo" style="width:250px"></div>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">备注</label>
					<div class="layui-input-block">
						<input type="text" name="Remark" id="Remark" placeholder="请输入" autocomplete="off" class="layui-input">
					</div>
				</div>
				
				<div class="layui-form-item">
					<div class="layui-input-block">
						<button type="button" class="layui-btn" lay-submit lay-filter="Plansubmit">提交</button>
						<button type="reset" class="layui-btn layui-btn-primary" id="btn_reset">重置</button>
					</div>
				</div>
			</form>
		</div>
	</div>



	<script src="~/layuiadmin/layui/layui.js"></script>
	<script src="~/layuiadmin/layuiextend/xm-select.js"></script>
	<script>
		layui.use(['jquery', 'layer', 'tree', 'form','laydate', 'element', 'upload', 'laytpl', 'table'], function () {
			var layer = layui.layer,
				$ = layui.$,
				table = layui.table,//表格
				laydate = layui.laydate,
				form = layui.form;

			// 初始化日期选择器
				laydate.render({
					elem: '#BrithDay' // 绑定元素
					, type: 'date'
					, format: 'yyyy-MM-dd' // 设置日期格式
					, done: function (value, date) {
					}
				});


		// 监听身份证号码输入框的变化
		$('#IDCardNo').on('input propertychange', function () {
			var idCard = $(this).val();
			var birthDate;

			// 检查身份证号码长度是否正确
			if (idCard.length === 18) {
				// 提取出生日期部分
				var birthStr = idCard.substr(6, 8);

				// 尝试将字符串转换为日期
				if (/^\d{4}(0[1-9]|1[012])(0[1-9]|[12][0-9]|3[01])$/.test(birthStr)) {
					birthDate = new Date(birthStr.slice(0, 4), parseInt(birthStr.slice(4, 6)) - 1, birthStr.slice(6, 8));
					var year = birthDate.getFullYear();
					var month = ('0' + (birthDate.getMonth() + 1)).slice(-2); // 补零
					var day = ('0' + birthDate.getDate()).slice(-2); // 补零
					var formattedDate = year + '-' + month + '-' + day;
					// 设置出生日期输入框的值
					$('#BrithDay').val(formattedDate);
					calculateAge(birthStr.slice(0, 4), parseInt(birthStr.slice(4, 6)) - 1, birthStr.slice(6, 8))
					form.render(); // 更新表单视图
				} else {
					alert('身份证号码中的日期部分无效！');
				}
			}

		});
			 var xmDeptList = xmSelect.render({
				el: '#xmDeptList',
				autoRow: true,
				radio: true,
				prop: {
					name: 'name',
					value: 'id',
				},
				minWidth: 350,
				height: 350,
				filterable: true,
				tips: '请选择科室',
				done: function (res) {
				}
			})

			var xmDeptList2 = xmSelect.render({
				el: '#xmDeptList2',
				autoRow: true,
				radio: true,
				prop: {
					name: 'name',
					value: 'id',
				},
				minWidth: 350,
				height: 350,
				filterable: true,
				tips: '请选择科室',
				done: function (res) {
				}
			})
			var xmTempList = xmSelect.render({
				el: '#GetTempList',
				autoRow: true,
				radio: true,
				prop: {
					name: 'name',
					value: 'id',
				},
				minWidth: 350,
				height: 350,
				filterable: true,
				tips: '请选择模板',
				done: function (res) {
				},
				 on: function (val) {
					if (val.arr && val.arr.length > 0) {
						GetTempDetailList(val.arr[0].id);
					}
				}
			})
			var xmTempDetailList = xmSelect.render({
				el: '#GetTempDetailList',
				autoRow: true,
				radio: false,
				prop: {
					name: 'name',
					value: 'id',
				},
				minWidth: 350,
				height: 350,
				filterable: true,
				tips: '请选择模板详情',
				done: function (res) {

				}
			})

			function GetDeptList() {
				$.ajax({
					url: '/PatientManage/PatientManage/GetDeptList',
					type: "get",
					datatype: 'json',
					success: function (result) {
						xmDeptList.update({
							data: result.data
						});
					}, error: function () {
						layer.msg("获取失败！");
					}
				});
			}

			function GetDeptList2() {
				$.ajax({
					url: '/PatientManage/PatientManage/GetDeptList',
					type: "get",
					datatype: 'json',
					success: function (result) {
						xmDeptList2.update({
							data: result.data
						});
					}, error: function () {
						layer.msg("获取失败！");
					}
				});
			}
			function GetTempList() {
				$.ajax({
					url: '/PatientManage/PatientManage/GetTempList',
					type: "get",
					datatype: 'json',
					success: function (result) {
						xmTempList.update({
							data: result.data
						});
					}, error: function () {
						layer.msg("获取失败！");
					}
				});
			}
			function GetTempDetailList(id) {
				$.ajax({
					url: '/PatientManage/PatientManage/GetTempDetailList?Id='+id,
					type: "get",
					datatype: 'json',
					success: function (result) {
						xmTempDetailList.update({
							data: result.data
						});
					}, error: function () {
						layer.msg("获取失败！");
					}
				});
			}

			table.render({
				elem: '#tablelist'
				, id: 'tablelist'
				, page: true

				, limit: 20
				, height: 'full-105'
				, cols: [[
					{ field: 'No', title: '序号', type: 'numbers', fixed: 'left' }
					, { field: 'PatientId', title: '患者ID', width: 85, minWidth: 85 }
					, { field: 'PatientName', title: '患者姓名', width: 90, minWidth: 86 }
					, { field: 'PatientSource', title: '患者来源', width: 90, minWidth:86 }
					, { field: 'IDCardNo', title: '身份证号', width: 172, minWidth: 172 }
					, { field: 'Telephone', title: '电话号码', width: 125, minWidth: 125  }
					, { field: 'Sex', title: '性别', width: 60, minWidth: 60 }
					, { field: 'Age', title: '年龄', width: 60, minWidth: 60 }
					, { field: 'BrithDay', title: '出生日期', width: 105, minWidth: 105 }
					, { field: 'BRKH', title: '卡号', width: 60, minWidth: 60 }
					, { field: 'BLH', title: '住院号', width: 75, minWidth: 75 }
					, { field: 'Diagnosis', title: '诊断', width: 60, minWidth: 60 }
					, { field: 'ICDCode', title: 'ICD代码', width: 85, minWidth: 85 }
					, { field: 'HospitalDeptName', title: '科室名称', width: 85, minWidth: 85 }
					, { field: 'CreateUserName', title: '创建人' , width: 70, minWidth: 70 }
					, { field: 'CreatedTime', title: '创建时间',width: 160, minWidth: 160 }
					, { title: '操作', toolbar: '#tableBar', width: 227, minWidth: 227, fixed: 'right' }
				]]
				, done: function (res, curr, count) {
					var id = 0;
				}
			});

			function SearchData() {

				var Id = xmDeptList.getValue('valueStr');
				table.reload('tablelist', {
					page: {
						curr: 1
					},
					url: '/PatientManage/PatientManage/List'
					, where: {
						'keyWord': $.trim($("#keyWord").val()),
						'Deps':Id
					}
				});
			};


						//监听tablelist工具条
			table.on('tool(tablelist)', function (obj) {
				var data = obj.data;
				if (obj.event === 'del') {
					layer.confirm('确定要删除名为【' + data.PatientName + '】的患者信息吗？将无法恢复。', {
						title: '',
						btn: ['确定', '取消'], //按钮
						resize: false
					}, function (index) {
							$.post('/PatientManage/PatientManage/Del', { id: data.Id }, function (result) {
							if (result.okMsg) {
								layer.msg(result.okMsg);
								currentIndex = -1;
								table.reload('tablelist'); //重载表格
								EmptyData();
							} else {
								layer.msg(result.errorMsg);
							}
						}, 'json');
						layer.close(index);
					});
				}
				
				else if(obj.event === 'plan') {
					xmTempList.setValue([]);
					xmTempDetailList.setValue([]);
					$("#PlanId").val('');
					$("#PId").val(data.Id);
					$("#PlanPatientId").val(data.PatientId);

					windowsIndex = layer.open({
						type: 1,
						title: '创建患者随访计划',
						area: ['40%', '40%'],
						resize: true,
						content: $('#addPlan_window')
					});
				}
				else if(obj.event === 'edit') {
					var depid = data.HospitalDeptId;
					var sex = data.Sex == "男" ? "1" : "0";
					var source = data.PatientSource;
					$('[name="PatientSource"][value="' + source + '"]').prop('checked', true);
					$('[name="Sex"][value="' + sex + '"]').prop('checked', true);
					layui.form.render('radio');
					if (depid != undefined) {
					xmDeptList2.setValue([depid]);
					}
					$("#PatientId").val(data.PatientId);
					$("#PatientName").val(data.PatientName);
					$("#IDCardNo").val(data.IDCardNo);
					$("#Telephone").val(data.Telephone);
					$("#Age").val(data.Age);
					$("#BrithDay").val(data.BrithDay);
					$("#BRKH").val(data.BRKH);
					$("#BLH").val(data.BLH);
					$("#Diagnosis").val(data.Diagnosis);
					$("#ICDCode").val(data.ICDCode);
					$("#Id").val(data.Id);
					windowsIndex = layer.open({
						type: 1,
						title: '修改患者信息',
						area: ['40%', '95%'],
						resize: true,
						content: $('#Patient_window')
					});					
				}
			});

			//监听提交
			form.on('submit(Plansubmit)', function (data) {
				var indes = layer.load(1);
				var tempid = xmTempList.getValue('valueStr');
				var tempDetail = xmTempDetailList.getValue('valueStr');
				if (tempid == '') { layer.msg("请选择随访模板！"); layer.close(indes); return false; }
				if (tempDetail == '') { layer.msg("请选择随访模板详情！"); layer.close(indes); return false; }

				data.field.TemplateId = tempid;
				data.field.Description = tempDetail;
				data.field.PatientId = $("#PId").val();
				data.field.Remark = $("#Remark").val();
				data.field.Id = $("#PlanId").val();
				//提交 Ajax 成功后，关闭当前弹层并重载表格
				$.ajax({
					url: '/PatientManage/PatientManage/PlanSave',
					type: "post",
					data: { 'node': data.field },
					datatype: 'json',
					success: function (data) {
						if (data.okMsg) {
							layer.msg(data.okMsg);
							layer.close(windowsIndex);//关闭弹出层
						}
						else {
							layer.msg(data.errorMsg);
						}
						layer.close(indes);
					}, error: function (res) {
						layer.msg("加载统计信息错误：" + res.responseText);
						layer.close(indes);
					}
				});
				return false;
			});

			form.on('submit(submit)', function (data) {
				var indes = layer.load(1);
				var depid = xmDeptList2.getValue('valueStr');
				
				data.field.patientId = $("#PatientId").val();
				data.field.patientName = $("#PatientName").val();
				data.field.patientSource = $('input[name="PatientSource"]:checked').val();
				data.field.iDCardNo = $("#IDCardNo").val();
				data.field.telephone = $("#Telephone").val();
				data.field.sex = $('input[name="Sex"]:checked').val();
				data.field.age = $("#Age").val();
				data.field.brithDay = $("#BrithDay").val();
				data.field.bRKH = $("#BRKH").val();
				data.field.bLH = $("#BLH").val();
				data.field.diagnosis = $("#Diagnosis").val();
				data.field.iCDCode = $("#ICDCode").val();
				data.field.hospitalDeptId = depid;
				data.field.id = $("#Id").val();
				if (depid == '') { layer.msg("请选择科室！"); layer.close(indes); return false; }
				if (data.field.patientSource == undefined) { layer.msg("请选择患者来源!"); layer.close(indes); return false; }
				if (data.field.iDCardNo == '') { layer.msg("身份证号不能为空！"); layer.close(indes); return false; }
				else{
						// 正则表达式：兼容15位和18位身份证
						var regex = /(^\d{15}$)|(^\d{17}(\d|X|x)$)/;
						if (!regex.test(data.field.iDCardNo)) {
						  layer.msg("身份证号格式错误！");
						  layer.close(indes); return false;
						}

						// 进一步校验18位身份证的校验码（可选）
						if (data.field.iDCardNo.length === 18) {
						  var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
						  var checkCode = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
						  var sum = 0;
						  for (var i = 0; i < 17; i++) {
							sum += data.field.iDCardNo.charAt(i) * factor[i];
						  }
						  var code = checkCode[sum % 11];
						  if (data.field.iDCardNo.charAt(17).toUpperCase() !== code) {
							layer.msg("身份证校验码错误！");
						  layer.close(indes); return false;
						  }
						}
				}
				if (data.field.telephone != '') { // 正则表达式：支持最新号段（如199、166等）
				var regex = /^1[3-9]\d{9}$/;
				if (!regex.test(data.field.telephone)) {
					layer.msg("手机号格式错误！"); layer.close(indes); return false;
				}}
				if (data.field.sex == undefined) { layer.msg("请选择性别!"); layer.close(indes); return false; }
				if (data.field.age == '') { layer.msg("年龄不能为空！"); layer.close(indes); return false; }
				if (data.field.brithDay == '') { layer.msg("出生日期不能为空！"); layer.close(indes); return false; }
				
				//提交 Ajax 成功后，关闭当前弹层并重载表格
				$.ajax({
					url: '/PatientManage/PatientManage/Save',
					type: "post",
					data: { 'node': data.field },
					datatype: 'json',
					success: function (data) {
						if (data.okMsg) {
							layer.msg(data.okMsg);
							layer.close(windowsIndex);//关闭弹出层
							table.reload('tablelist'); //重载表格
						}
						else {
							layer.msg(data.errorMsg);
						}
						layer.close(indes);
					}, error: function (res) {
						layer.msg("加载统计信息错误：" + res.responseText);
						layer.close(indes);
					}
				});
				return false;
			});


			$(document).ready(function () {

				$(document).on('click', '#Search', function () {
					SearchData();
				})

				$(document).on('click', '#addPatient', function () {
					EmptyData();
					windowsIndex = layer.open({
						type: 1,
						title: '新增患者信息',
						area: ['40%', '95%'],
						resize: true,
						content: $('#Patient_window')
					});
					url = '/AIFollowUp/FollowUpTemplates/Save';
				});
			});
			function EmptyData() {

				xmDeptList2.setValue([]);
				$('input[name="PatientSource"]').prop('checked', false);
				$('input[name="Sex"]').prop('checked', false);
				form.render('radio');
				$("#PatientId").val('');
				$("#PatientName").val('');
				$("#IDCardNo").val('');
				$("#Telephone").val('');
				$("#Age").val('');
				$("#BrithDay").val('');
				$("#BRKH").val('');
				$("#BLH").val('');
				$("#Diagnosis").val('');
				$("#ICDCode").val('');
				$("#Id").val('');
			}

			function calculateAge(year, month, day) {
				var now = new Date();
				var birthDate = new Date(year, month - 1, day); // 注意月份是从0开始的
				var age = now.getFullYear() - birthDate.getFullYear();

				// 如果当前月份小于出生月份，或者当前月份等于出生月份但当前日小于出生日，则年龄减一
				if (now.getMonth() < birthDate.getMonth() ||
					(now.getMonth() === birthDate.getMonth() && now.getDate() < birthDate.getDate())) {
					age--;
				}

				$('#Age').val(age);
		}

			//患者列表的高度
			function setPatientListH() {
				var winH = $(window).height();
				var screeH = $(".search_wrap").height();
				var pagebtnH = $(".pagebtn").height();
				var listH = winH - (screeH + pagebtnH) - 70 + "px";
				$(".patient_list").css("height", listH);
			}
			setPatientListH();
			GetDeptList();
			GetDeptList2();
			GetTempList();
			SearchData();

			$(window).resize(function () {
				setPatientListH();
			});




		})
	</script>



</body>
</html>