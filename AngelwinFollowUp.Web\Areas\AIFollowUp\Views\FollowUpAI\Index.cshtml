﻿@{
    ViewBag.Title = "AI随访";
    Layout = null;
}
@model AngelwinFollowUp.Models.ResearchPatient
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title> @ViewBag.SiteTitle</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/layuiadmin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/layuiadmin/style/admin.css" rel="stylesheet" />
    <link rel="stylesheet" href="~/lib/dialog/style.css">
    <link rel="stylesheet" href="~/lib/audio/css/audio_style.css">

    <script src="~/js/jquery-3.5.1.min.js"></script>
    <!--录音合成-->
    <script src="~/KeDaXunFei/base64.js"></script>
    <script src="~/KeDaXunFei/crypto-js.js"></script>
    <script src="~/KeDaXunFei/tts/dist/index.umd.js"></script>
    <!--语音转写-->
    <script src="~/KeDaXunFei/hmac-sha256.js"></script>
    <script src="~/KeDaXunFei/HmacSHA1.js"></script>
    <script src="~/KeDaXunFei/md5.js"></script>
    <script src="~/KeDaXunFei/enc-base64-min.js"></script>
    <script src="~/KeDaXunFei/dist/index.umd.js"></script>
    <script type="text/javascript" src="~/KeDaXunFei/HZRecorder.js?v=6"></script>
    <script src="~/js/marked.min.js"></script>
    <style>
        .layui-panel {
            background-color: #fff;
            padding: 20px;
        }

        .patient_info {
            border-bottom: 1px solid #eee;
        }

        .flex-row {
            display: flex;
            flex-direction: row;
            align-items: center;
        
        }

        .space_between{
            justify-content: space-between;
        }

        .flex_wrap {
            flex-wrap: wrap;
        }

        .user_name {
            min-width: 120px;
            line-height: 30px;
            font-size: 18px;
            font-weight: bold;
        }

        .sex {
            font-size: 14px;
            font-weight: normal;
        }

        .sex0 {
            color: #FF5722;
        }

        .sex1 {
            color: #1E9FFF;
        }

        .user_value {
            color: #000;
        }

        .info_item {
            padding: 6px 0;
            padding-left: 15px;
        }

        .content_wrap {
            margin: 6px 0;
        }

        .list_wrap {
            height: 90vh;
            background-color: #F9FBFF;
            padding: 1px;
        }

        .Follow-up_content {
            height: 89vh;
            padding: 5px;
            background-color: #fff;
        }

        /* 内容区 */

        /* 通用样式 */
        .logo_pic_container {
            display: inline-block;
            vertical-align: middle;
        }

        /*.logo_pic {
            width: 30px;*/ /* 调整图标大小 */
        /*height: 30px;
            border-radius: 50%;*/ /* 圆角效果 */
        /*}*/

        /*.text_wrap {
            display: inline-block;
            vertical-align: middle;
            max-width: 80%;*/ /* 根据需要调整宽度 */
        /*}*/

        /* 特定于助手的回答 */
        .answer .logo_pic_container {
            float: left;
            margin-right: 10px; /* 添加一些间距 */
        }

        /* 特定于用户的提问 */
        .quizzer .logo_pic_container.user_logo {
            float: right;
            margin-left: 10px; /* 添加一些间距 */
        }

        /* 清除浮动 */
        .answer, .quizzer {
            clear: both;
            overflow: hidden; /* 确保容器包裹浮动元素 */
        }

        .dotwrap {
            margin-right: 5px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            border: 1px solid #ccc;
            padding: 2px;
            background-color: transparent;
        }

        .doc {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-color: #ccc;
        }

        .doc_check {
            color: #FFB800;
            font-weight: bold;
        }

        .type1 {
            margin-right: 10px;
            color: #7a4d7b;
        }

        .userinfo {
            display: flex;
            flex-direction: row;
            align-items: center;
            color: #fff;
        }

        .userinfo_val {
            padding: 0 5px;
        }

        .prompt {
            margin: 5px 15px;
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        .operating {
            width: 100%;
        }

        .input_wrap {
            width: 100%;
            display: flex;
            flex-direction: row;
            align-items: center;
            margin: 5px 15px;
            background-color: #fff;
            border: 1px solid #7a4d7b;
            border-radius: 6px;
            overflow: hidden;
            margin-top: 18px;
        }

            .input_wrap .layui-icon {
                color: #7a4d7b;
                padding: 12px;
            }

            .input_wrap .layui-input {
                border: none;
                padding-left: 0;
            }

                .input_wrap .layui-input:hover {
                    border: none;
                }

        .layui-icon-release {
            background-color: #7a4d7b;
            color: #fff !important;
            cursor: pointer;
        }


        /*对话去区*/

        .dialog_box {
            padding: 10px;
            overflow-y: auto;
            font-size: 16px;
        }

        .quizzer {
            padding: 10px 0;
            display: flex;
            justify-content: flex-end;
        }

        .text_wrap {
            max-width: 80%;
            background-color: #eff6ff;
            padding: 10px;
            margin-right: 10px;
            border-radius: 6px 0 6px 6px;
        }

        .answer {
            display: flex;
            flex-direction: row;
        }

        .answer_inner {
            flex-grow: 1;
            padding: 10px;
        }

        .logo_pic {
            flex-grow: 0;
            flex-shrink: 0;
            width: 40px;
            height: 40px;
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid #eee;
            margin-right: 10px;
            border-radius: 50%;
        }

            .logo_pic img {
                width: 100%;
            }

        .layui-form-select dl dd.layui-this {
            background-color: #7a4d7b;
        }



        /* 随访列表 */
        .Follow-up_list {
            height: 84vh;
            overflow-y: auto;
        }

        .list_title {
            line-height: 50px;
            padding-left: 16px;
            font-size: 16px;
        }

        .active {
            background: linear-gradient(70deg, #dcffff, #62ffff);
            border-color: transparent;
            color: #1E9FFF;
        }

        .nav_item {
            border-top-color: rgb(238, 238, 238);
            border-right-color: rgb(238, 238, 238);
            border-bottom-color: rgb(238, 238, 238);
            border-left-color: rgb(238, 238, 238);
            padding: 10px 15px;
            border-radius: 4px;
            margin: 0 5px 10px;
        }

        .title_wrap {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .micro_btn {
            width: 120px;
            height: 30px;
            flex: 1;
        }

            .micro_btn .layui-btn {
                width: 120px;
                height: 44px;
                line-height: 44px;
                margin: 0;
                border-radius: 6px;
            }
    </style>

</head>
<body class="layui-layout-body">
    <div class="layui-panel flex-row space_between">
        <div class="flex-row userwrap">
            <div class="user_name">@Model.PatientName <span class="sex sex0">@Model.Sex</span></div>
            <div class="user_info flex-row flex_wrap">
                <div class="info_item">编号：<span class="user_value">@Model.PatientId</span></div>
                <div class="info_item">科室：<span class="user_value">@Model.HospitalDept.Name</span></div>
                <div class="info_item">临床诊断：<span class="user_value">@Model.Diagnosis</span></div>
            </div>
        </div>
        <div class="template_btn">
            <button type="button" class="layui-btn layui-bg-blue" id="templateBtn"><i class="layui-icon layui-icon-shrink-right"></i>随访模板</button>
        </div>
    </div>
    <div class="content_wrap layui-row">
        <div class="layui-col-xs2">
            <div class="list_wrap">
                <div class="list_title">随访记录</div>
                <!--<div style="display:none">
                    <div>语速([0-100])：<input id="speed" value="50" /></div>
                    <div>音量([0-100])：<input id="volume" value="50" /></div>
                    <div>音高([0-100])：<input id="pitch" value="50" /></div>
                    <div>发音人：<input id="vcn" value="xiaoyan" /></div>-->
                    <!-- x4_lingxiaolu_en -->
                    <!--<div>小语种(泰语)：<input id="tte" type="checkbox" /></div>
                    <div>注意：发音人与语种会关联，小语种需要开通指定发音人</div>
                </div>-->
                <ul class="Follow-up_list" id="followupList">
                    <!-- 动态生成随访记录列表 -->
                </ul>
            </div>
        </div>
        <div class="layui-col-xs6 layui-col-xs10" id="dialogWrap">
            <div class="Follow-up_content">
                <div class="dischargeSummary">
                    <div class="dialog_box" id="dialogBox">
                        <!-- 动态生成随访记录详情 -->
                    </div>
                </div>

                <div class="operating layui-form flex-row">
                    <div class="input_wrap">
                        <div class="layui-input-split layui-input-prefix">
                            <i class="layui-icon layui-icon-dialogue"></i>
                        </div>
                            <input type="text" placeholder="请输入···" class="layui-input" id="Input" />
                            <input style="display:none" type="text" placeholder="请输入···" class="layui-input" id="Input2" />
                            <i  class="layui-icon layui-icon-release" id="InputBtn"></i>
                    </div>
                    <div class="micro_btn">
                        <div id="btnRecord1" class="layui-btn layui-btn-primary layui-border-blue mike1"><i class="layui-icon layui-icon-mike "></i>开启录音</div>
                        <div class="layui-btn layui-btn-normal mike0" style="display:none;"><i class="layui-icon layui-icon-mike mike0"></i>结束录音</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-xs4">
            <div class="template_text">
                <textarea name="Description" id="Description" placeholder="请输入随访描述" class="layui-textarea" style="resize: none; width: 100%;"></textarea>
            </div>
        </div>
    </div>
    <script src="~/layuiadmin/layui/layui.js"></script>
    <script src="~/KeDaXunFei/tts/tts.js?v=3.14154"></script>
    <script>
         var APPID = "@ViewBag.XuFeiAPPID";
        var API_KEY = "@ViewBag.XuFeiAPI_KEY";
        var TTS_APPID="@ViewBag.XuFei_TTS_APPID";
        var TTS_API_KEY = "@ViewBag.XuFei_TTS_API_KEY";
        var TTS_API_SECRET = "@ViewBag.XuFei_TTS_API_SECRET";
        const btnControl = document.getElementById("btnRecord1");
         loadJs();
         layui.use(['jquery', 'layer', 'tree', 'form', 'element', 'upload', 'laytpl', 'table'], function () {
                var layer = layui.layer,
                    $ = layui.$;

                var currentFollowupRecordId;
                var currentPatientId;
                var currentPlanId;
                var currentTemplateDetailId;
                var submitCount = 0;
                var patientId = @ViewBag.PatientId;
                var templateId = @ViewBag.TemplateId;



                //样式-可折叠的侧边面板
                $("#templateBtn").on("click",function(){
                    var isShow= $("#dialogWrap").hasClass("layui-col-xs10");
                    if(isShow){
                        $("#dialogWrap").removeClass("layui-col-xs10");
                        $(this).find("i").removeClass("layui-icon-shrink-right");
                        $(this).find("i").addClass("layui-icon-spread-left");
                    }else{
                        $("#dialogWrap").addClass("layui-col-xs10");
                        $(this).find("i").removeClass("layui-icon-spread-left");
                        $(this).find("i").addClass("layui-icon-shrink-right");
                    }
         
                })




                 
                $(document).ready(function () {
                    // var patientId = '@Model.Id';
                    getFollowupRecords(patientId,templateId); //获取患者ID为1的随访记录

                    $(document).on('click', '#InputBtn', function () {
                        $(this).css("display", "none");
                        $(this).prop('disabled', true);
                        submitBtn();
                    });
                    // 监听文本框中的按键事件
                    $("#Input").on("keypress", function (e) {
                        if (e.which === 13) {
                            $("#InputBtn").css("display", "none");
                            submitBtn();
                        }
                    });

                  

                     // 添加页面离开事件监听器，停止所有语音播放
                    $(window).on('unload', function() {
                        stopAllAudio();
                    });

                    // 添加页面隐藏事件监听器，停止所有语音播放
                    $(document).on('visibilitychange', function() {
                        if (document.hidden) {
                            stopAllAudio();
                        }
                    });

                    // 监听点击链接事件，确保在导航到其他页面时停止语音
                    $(document).on('click', 'a', function() {
                        // 检查链接是否指向其他页面
                        var href = $(this).attr('href');
                        if (href && href !== '#' && !href.startsWith('javascript:')) {
                            stopAllAudio();
                        }
                    });

                });
               
                 document.addEventListener('visibilitychange', function() {
                      // 页面变为不可见时触发
                      if (document.visibilityState == 'hidden') {
                       //stopAllAudio();
                       console.log('stopAllAudio');
                      }
                    });

                // 定义停止所有语音播放的函数
                function stopAllAudio() {
                    // 停止并重置当前正在播放的 audioPlayer
                    if (typeof audioPlayer !== "undefined") {
                        tts_btnStatus = "UNDEFINED";
                        ttsWS?.close();
                        audioPlayer.reset();
                    }
                    // 清除定时器
                    CloseTimer();
                    // 移除所有播放状态
                    $(".audio_area").removeClass("playing");
                }

                //新曾录音按钮
             $(".micro_btn").on("click", "div", function () {
                 $(this).css("display", "none")
                 $(this).siblings().css("display", "block");

                 var ismike = $(this).hasClass("mike1");
                 if (ismike) {
                     $("#InputBtn").css("display", "none"); // 隐藏文字发送 按钮

                     // 关闭所有正在播放的语音动画
                     $(".audio_area").removeClass("playing");
                     tts_btnStatus = "UNDEFINED";
                     ttsWS?.close();
                     audioPlayer.reset();

                     startRecords("Input");//开始录音
                 } else {
                     //点击了"结束录音"
                     if (btnStatus === "CONNECTING" || btnStatus === "OPEN") {
                         // 结束录音
                         recorder.stop();
                         submitBtn();
                         $("Input").val("");
                     }
                 }

             });




                function setDialogBoxH() {
                    var winH = $(window).height();
                    var userwrapH = $(".userwrap").height();
                    var operatingH = $(".operating").height();
                    var DialogBox = winH - (userwrapH + operatingH) - 100;
                    var DescriptionH = winH - (userwrapH + operatingH)+20;
                    $(".dialog_box").css("height", DialogBox + "px");
                    $("#Description").css("height", DescriptionH + "px");

                };
                setDialogBoxH();

                $(window).resize(function () {
                    setDialogBoxH();
                });


             $(".Follow-up_list").on("click", ".nav_item", function () {
                 if ($(this).data("clicked")) {
                     // 如果已经点击过，直接返回，禁止双击
                     return;
                 }
                 // 设置标志，表示已经点击
                 $(this).data("clicked", true);

                 // 移除其他项的点击标志
                 $(".nav_item").not(this).removeData("clicked");


                  // 添加激活状态
                 $(".nav_item").removeClass("active");
                 $(this).addClass("active");

                 currentFollowupRecordId = $(this).data("recordid");
                 currentPatientId = $(this).data("patientid");
                 currentDescription = $(this).data("description");
                 currentPlanId = $(this).data("id");
                 currentTemplateDetailId = $(this).data("templatedetailid");
                 getFollowupRecordDetails(currentFollowupRecordId);// 获取随访记录详情
                 checkRecordDetail(currentFollowupRecordId);//校验是否有随访对话数据
                 getDescription(currentFollowupRecordId);//获取模版内容
                 // 设置一个定时器，解除禁止双击的状态
                 setTimeout(() => {
                     $(this).removeData("clicked");
                 }, 500); // 500 毫秒内禁止双击
             });


                // 获取随访记录列表
                function getFollowupRecords(patientId,templateId) {
                    $.ajax({
                        url: '/AIFollowUp/FollowUpAI/GetFollowupRecords?patientId=' + patientId + '&templateId=' + templateId,
                        type: 'GET',
                        success: function (data) {
                            console.log(data);
                            var followupList = $("#followupList");
                            followupList.empty();
                            $.each(data, function (index, item) {
                                var listItem = '<li class="nav_item" data-id="' + item.id + '" data-patientid="' + item.patientId + '" data-description="' + item.description + '" data-planid="' + item.id + '" data-templatedetailid="' + item.templateDetailId + '" data-recordid="' + item.recordId + '">' +
                                    '<div class="title_wrap">' +
                                    '<div style="display:none;">' + item.id + '</div>' +
                                    '<div>' + item.templateDetailName + '</div>' +
                                    '<div>' + item.followupDate + '</div>' +
                                    '</div>' +
                                    '</li>';
                                followupList.append(listItem);
                            });

                            // 模拟点击第一条数据
                            if (data.length > 0) {
                                $(".nav_item:first").click();
                            }
                        },
                        error: function (xhr, status, error) {
                            layer.alert("获取随访记录失败：" + error);
                        }
                    });
                }


                // 校验是否有随访对话数据
                function checkRecordDetail(followupRecordId) {
                    $.ajax({
                        url: '/AIFollowUp/FollowUpAI/CheckRecordDetailData?followupRecordId=' + followupRecordId,
                        type: 'GET',
                        success: function (data) {
                            console.log(data);
                            if (data == false) {
                                var inputContent = "开始随访";
                                startChatWithAI1(followupRecordId, currentPatientId, inputContent, currentPlanId, currentTemplateDetailId);
                                enableForm();
                            } else if (data == true) {
                                // 停止并重置当前正在播放的 audioPlayer
                                if (typeof audioPlayer !== "undefined") {
                                    tts_btnStatus = "UNDEFINED";
                                    ttsWS?.close();
                                    audioPlayer.reset();
                                }
                                //输入框按钮置灰
                                //disableForm();
                            }
                        },
                        error: function (xhr, status, error) {
                            layer.alert("失败：" + error);
                        }
                    });
                }

                function disableForm() {
                    // 选择具有 'operating layui-form' 类的 div，并将其透明度设置为 0.2
                    $('.operating.layui-form').css({
                        'opacity': '0.2', // 改变透明度，使其看起来被置灰
                        'pointer-events': 'none' // 禁用点击交互
                    });

                    // 禁用所有表单控件
                    $('.operating.layui-form input, .operating.layui-form button').attr('disabled', 'disabled');
                }

                function enableForm() {
                    // 选择具有 'operating layui-form' 类的 div，重置其透明度和pointer-events
                    $('.operating.layui-form').css({
                        'opacity': '', // 清除透明度设置
                        'pointer-events': '' // 重新启用点击交互
                    });

                    // 启用所有表单控件
                    $('.operating.layui-form input, .operating.layui-form button').removeAttr('disabled');
                }




                // 获取随访记录详情
             function getFollowupRecordDetails(followupRecordId) {
                    $.ajax({
                        url: '/AIFollowUp/FollowUpAI/GetFollowupRecordDetails?followupRecordId=' + followupRecordId,
                        type: 'GET',
                        success: function (data) {
                            var dialogBox = $("#dialogBox");
                            dialogBox.empty();
                            $.each(data, function (index, item) {
                                var contentClass = item.createUserName === 'assistant' ? 'answer' : 'quizzer';
                                var logoPic;
                                if (item.createUserName === 'assistant') {
                                    // 助手的回答：图片在左侧
                                    logoPic = '<div class="logo_pic_container"><img src="@Url.Content("~/images/caduceus.png")" class="logo_pic"/></div>';
                                } else {
                                    // 用户的问题：图片在右侧
                                    logoPic = '<div class="logo_pic_container user_logo"><img src="@Url.Content("~/images/user_pic.png")" class="logo_pic"/></div>';
                                }


                                var audiowrap = `
                                    <p class="weixinAudio">
                                        <span class="db audio_area">
                                            <span class="audio_wrp db">
                                                <span class="audio_play_area">
                                                    <i class="icon_audio_default"></i>
                                                    <i class="icon_audio_playing"></i>
                                                </span>
                                            </span>
                                        </span>
                                    </p>`;

                                var textWrap = '<div class="text_wrap">' +
                                    '<p id="content_' + index + '">' + item.content + '</p >' +
                                    (item.createUserName === 'assistant' ? '<div class="audio_content" style="cursor: pointer;"  xh="' + index + '" id="audio_' + index + '">' +
                                        audiowrap +
                                        '</div>' : '') + '</div>';

                                var content = '<div class="' + contentClass + '">' +
                                    (item.createUserName === 'assistant' ? logoPic + textWrap : textWrap + logoPic) +
                                    '</div>';
                                dialogBox.append(content);
                            });

                             //动态绑定点击事件 //修改绑定事件 addbyzolf 20250322
                            $('.audio_content').on('click', function () {
                                var xh = $(this).attr("xh");
                                var text = $.trim($("#content_" + xh).html());
                                RadioPlay(xh, text);
                                });
                        },
                        error: function (xhr, status, error) {
                            layer.alert("获取随访记录详情失败：" + error);
                        }
                    });
                    setTimeout(function () {
                        resetHistoryscrollTop(0);
                    }, 500);
                }



                // 启动与AI助手的对话
             function startChatWithAI1(followupRecordId, patientId, inputContent, planId, templateDetailId) {


                 voiceTextAll = "";
                 voiceTextSend = "";
                 submitCount = submitCount + 1;
                 RadioPlayStream("new" + submitCount);
                 //var modelName = "ChatGLM3";
                 var source = new EventSource('/AIFollowUp/FollowUpAI/GetChatStreamAnswer?followupRecordId=' + followupRecordId + "&planId=" + planId + "&patientId=" + patientId + "&templateDetailId=" + templateDetailId + "&prompt=" + encodeURIComponent(inputContent));// + "&modelType=" + modelName
                    var dialogBox = $("#dialogBox");
                    var audiowrap = `
                    <p class="weixinAudio">
                        <span class="db audio_area">
                            <span class="audio_wrp db">
                                <span class="audio_play_area">
                                    <i class="icon_audio_default"></i>
                                    <i class="icon_audio_playing"></i>
                                </span>
                            </span>
                        </span>
                    </p>`;
                 var i = 0;
                 source.onmessage = function (event) {
                        var result = JSON.parse(event.data);
                     if (result.okMsg) {
                         if (i == 0) {
                             i++;
                                var content = '<div class="answer"><div class="logo_pic"><img src="@Url.Content("~/images/caduceus.png")" /></div>' +
                                    '<div class="text_wrap"><p id="content_new' + submitCount + '">' + result.data + '</p>' +
                                    '<div class="audio_content" style="cursor: pointer;" xh="new' + submitCount + '"  id="audio_new' + submitCount + '">' + audiowrap + '</div> '
                                '</div></div > ';
                                dialogBox.append(content);
                            } else {
                                //$(".answer:last .text_wrap p").append(result.data);
                                var lastAnswer = $(".answer:last .text_wrap");
                                $("#content_new" + submitCount).append(result.data);
                                if (lastAnswer.find(".audio_content").length === 0) {
                                    lastAnswer.append('<div class="audio_content" id="audio_new' + submitCount + '">' + audiowrap + '</div>');
                                }
                            }
                            voiceTextAll = voiceTextAll + result.data;

                        } else {
                            layer.msg(result.errorMsg);
                            source.close();
                        }
                        resetHistoryscrollTop(0);
                    };

                 source.addEventListener('end', function (event) {
                        //点击按钮时需要放开2个样式
                        $("#InputBtn").css("display", "block");
                        $("#InputBtn").prop('disabled', false);
                        var result = JSON.parse(event.data);
                        if (result.okMsg) {
                            var recodId = result.recordId;
                            $(".nav_item.active").attr("data-recordid", recodId);
                            // 更新当前的followupRecordId变量
                            currentFollowupRecordId = recodId;
                            $("#Input").val(""); // 清空输入框

                            //新增区域绑定事件 addbyzolf 20250322
                            $("#audio_new" + submitCount).on('click', function () {
                                var xh = $(this).attr("xh");
                                var text = $.trim($("#content_" + xh).html());
                                RadioPlay(xh, text);
                            });
                        }
                        else {
                            layer.msg(result.errorMsg);
                            CloseTimer();
                        }
                        // 结束事件源连接
                        source.close();

                    }, false);
                 source.onerror = function (event) {
                     CloseTimer();
                     source.close();
                 };



                }

             function submitBtn() {
                    var inputContent = $("#Input").val().replace(/\s+/g, ''); // 去掉空格
                    if (inputContent.trim() === "") {
                        layer.alert("请输入内容");
                        return;
                    }

                    // 用户输入回答
                    var dialogBox = $("#dialogBox");
                    var content = '<div class="quizzer">' +
                        '<div class="text_wrap"><p>' + inputContent + '</p></div>' +
                        '<div class="logo_pic_container user_logo"><img src="@Url.Content("~/images/user_pic.png")" class="logo_pic"/></div>' +
                        '</div>';
                    dialogBox.append(content);
                    resetHistoryscrollTop(0);

                    // 调用 AI
                    startChatWithAI1(currentFollowupRecordId, currentPatientId, inputContent, currentPlanId, currentTemplateDetailId);


                }


                //设置Y轴位置
             function resetHistoryscrollTop(update) {
                    var ele = document.getElementById("dialogBox");
                    if (update == 1) {
                        leftContentScorll.update();
                    }
                    if (ele.scrollHeight > ele.clientHeight) {
                        setTimeout(function () {
                            //设置滚动条到最底部
                            ele.scrollTop = ele.scrollHeight;
                        }, 500);
                    }
                }

                 // 获取模版内容
            function getDescription(currentFollowupRecordId) {
                $.ajax({
                    url: '/AIFollowUp/FollowUpAI/GetTemplatesContent?followupRecordId=' + currentFollowupRecordId,
                    type: 'GET',
                    success: function (data) {
                        if(data){
                        $("#Description").val(data);
                        }
                    },
                    error: function (xhr, status, error) {
                        layer.alert("失败：" + error);
                    }
                });
            }

            });


        function loadJs() {
            var kdxf = "/KeDaXunFei/rtasr/XunFeiRecord.js?v=" + Math.random();
            var script = document.createElement('script');
            script.src = kdxf;
            script.type = 'text/javascript';
            script.id = "yuyin";

            document.head.appendChild(script);
        };


    </script>
</body>
</html>