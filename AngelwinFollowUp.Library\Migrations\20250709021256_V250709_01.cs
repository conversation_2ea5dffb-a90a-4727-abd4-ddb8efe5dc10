﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AngelwinFollowUp.Library.Migrations
{
    /// <inheritdoc />
    public partial class V250709_01 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "AITaskTypeId",
                table: "FollowupTemplates",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "AITaskTypeId",
                table: "FollowupRecords",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "AITaskTypes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    TypeName = table.Column<int>(type: "int", maxLength: 100, nullable: false),
                    Intro = table.Column<int>(type: "int", maxLength: 2000, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AITaskTypes", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_FollowupTemplates_AITaskTypeId",
                table: "FollowupTemplates",
                column: "AITaskTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_FollowupRecords_AITaskTypeId",
                table: "FollowupRecords",
                column: "AITaskTypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_FollowupRecords_AITaskTypes_AITaskTypeId",
                table: "FollowupRecords",
                column: "AITaskTypeId",
                principalTable: "AITaskTypes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_FollowupTemplates_AITaskTypes_AITaskTypeId",
                table: "FollowupTemplates",
                column: "AITaskTypeId",
                principalTable: "AITaskTypes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FollowupRecords_AITaskTypes_AITaskTypeId",
                table: "FollowupRecords");

            migrationBuilder.DropForeignKey(
                name: "FK_FollowupTemplates_AITaskTypes_AITaskTypeId",
                table: "FollowupTemplates");

            migrationBuilder.DropTable(
                name: "AITaskTypes");

            migrationBuilder.DropIndex(
                name: "IX_FollowupTemplates_AITaskTypeId",
                table: "FollowupTemplates");

            migrationBuilder.DropIndex(
                name: "IX_FollowupRecords_AITaskTypeId",
                table: "FollowupRecords");

            migrationBuilder.DropColumn(
                name: "AITaskTypeId",
                table: "FollowupTemplates");

            migrationBuilder.DropColumn(
                name: "AITaskTypeId",
                table: "FollowupRecords");
        }
    }
}
