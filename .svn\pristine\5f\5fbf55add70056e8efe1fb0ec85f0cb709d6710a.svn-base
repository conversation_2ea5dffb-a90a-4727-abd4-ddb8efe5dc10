﻿using AngelwinFollowUp.ModelExtends;
using AngelwinFollowUp.Models;
using AngelwinFollowUp.Services;
using AngelwinFollowUp.Web.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics;

namespace AngelwinFollowUp.Web.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;

        private IConfiguration Configuration { get; }
        private readonly IWebHostEnvironment env;
        private readonly AngelwinFollowUpDbContext db;

        public HomeController(ILogger<HomeController> logger, IConfiguration configuration, IWebHostEnvironment _env, AngelwinFollowUpDbContext _db)
        {
            Configuration = configuration;
            env = _env;
            db = _db;
            _logger = logger;
        }


        public async Task<IActionResult> Index()
        {
            string? currentUserName = User?.Identity?.Name;
            var user = db.Users.Include(i => i.Roles).FirstOrDefault(i => i.UserName == currentUserName);
            ViewBag.trueName = user?.TrueName;
            ViewBag.SiteTitle = Configuration["AppSettings:SiteTitle"];

            //#region 根据配置是否获取anyreport的ticket
            //ViewBag.Ticket = "";
            //var IsLoginAnyReport = "0";
            //if (!string.IsNullOrEmpty(Configuration["AppSettings:IsLoginAnyReport"]))
            //{
            //    IsLoginAnyReport = Configuration["AppSettings:IsLoginAnyReport"];
            //}
            //if (IsLoginAnyReport == "1")
            //{
            //    ViewBag.Ticket = GetAnyreportTicket()?.Result;
            //}
            //#endregion

            var username = user?.UserName;
            var uml = new UserMenuList();
            var ump = new UserMenuList();
            List<MenuTreeDTO> MenuTreeDTOList = null;
            if (username == "admin")          //如果是管理员，则加载所有菜单
            {
                uml.Menus = await db.Menus.AsNoTracking().ToListAsync();
            }
            else
            {
                var user_roles = user?.Roles.ToList();
                var rolesid = user_roles?.Select(c => c.RoleId).ToList();
                List<RoleInfo> roles = db.Roles.Include(i => i.Menus).ThenInclude(i => i.Menu).Where(i => rolesid.Contains(i.Id)).ToList();

                foreach (var r in roles)
                {
                    if (uml.Menus == null)
                    {
                        //uml.Menus = r.Menus.Select(c => c.Menu).ToList();
                        uml.Menus = r.Menus != null ? r.Menus.Where(c => c.Menu != null).Select(c => c.Menu!).ToList() : new List<Menu>();
                    }
                    else
                    {
                        if (uml.Menus != null && r.Menus != null)
                        {
                            //uml.Menus = uml.Menus.Union(r.Menus.Select(c => c.Menu).ToList());
                            uml.Menus = uml.Menus.Union(r.Menus.Select(c => c.Menu!).ToList()).ToList();
                        }
                        else if (uml.Menus == null && r.Menus != null)
                        {
                            //uml.Menus = r.Menus.Select(c => c.Menu).ToList();
                            uml.Menus = r.Menus.Select(c => c.Menu!).ToList();
                        }
                    }
                }
            }

            if (uml.Menus != null)
            {
                var rootMenu = db.Menus.FirstOrDefault(i => i.menuType == "根模块");
                if (rootMenu != null)
                {
                    var rootMenuId = rootMenu.Id;
                    var catalogMenu = uml.Menus.Where(i => i.parentId == rootMenuId && i.isMenu == "是").OrderBy(i => i.parentId).ThenBy(i => i.menuOrder).ToList(); //非菜单模块不加载
                    MenuTreeDTOList = (from r in catalogMenu
                                       select new MenuTreeDTO
                                       {
                                           MenuID = r.Id,
                                           MenuName = r.menuName,
                                           ParentID = r.parentId,
                                           MenuOrder = r.menuOrder,
                                           MenuIcon = r.menuIcon,
                                           MenuController = string.IsNullOrWhiteSpace(r.menuController) ? "" : r.menuController,
                                           MenuArea = string.IsNullOrWhiteSpace(r.menuArea) ? "" : r.menuArea,
                                           MenuAction = string.IsNullOrWhiteSpace(r.menuAction) ? "" : r.menuAction,
                                           Children = GetChildTree(uml.Menus.ToList(), r.Id)
                                       }).OrderBy(c => c.MenuOrder).ToList();
                }
            }
            else
            {
                return Redirect("/Account/LogOff");
            }
            LoginLog login = CommonFunction.GetLoginInfo(db, HttpContext.User.Identity.Name);
            ump.LastLoginIp = login.LastLoginIp == null ? "" : login.LastLoginIp;
            ump.LastLoginTime = login?.LastLoginTime?.ToString()??"";
            return View(MenuTreeDTOList);
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }

        public static List<MenuTreeDTO> GetChildTree(List<Menu> list, int Id)
        {

            List<MenuTreeDTO> tree = new List<MenuTreeDTO>();
            List<Menu> ChildList = GetChildList(list, Id);
            foreach (var r in ChildList)
            {
                MenuTreeDTO treeB = new MenuTreeDTO();
                treeB.MenuID = r.Id;
                treeB.MenuName = r.menuName;
                treeB.MenuIcon = r.menuIcon;
                treeB.MenuOrder = r.menuOrder;
                treeB.MenuController = string.IsNullOrWhiteSpace(r.menuController) ? "" : r.menuController;
                treeB.MenuArea = string.IsNullOrWhiteSpace(r.menuArea) ? "" : r.menuArea;
                treeB.MenuAction = string.IsNullOrWhiteSpace(r.menuAction) ? "" : r.menuAction;
                treeB.Children = GetChildTree(list, r.Id);
                tree.Add(treeB);
            }
            return tree;
        }

        public static List<Menu> GetChildList(List<Menu> list, int Id)
        {
            var childList = list.Where(x => x.parentId == Id).OrderBy(o => o.menuOrder).ToList();
            return childList;
        }

        public IActionResult Welcome()
        {
            return View();
        }

        public IActionResult changelog()
        {
            return View();
        }
    }
}
