﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AngelwinFollowUp.Library.Migrations
{
    /// <inheritdoc />
    public partial class V250709_03 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AITaskCRFDatas",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PatientId = table.Column<int>(type: "int", nullable: false),
                    AITaskTypeId = table.Column<int>(type: "int", nullable: false),
                    FollowupRecordId = table.Column<int>(type: "int", nullable: false),
                    FollowupRecordDetailIds = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    CRFormId = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    FormName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    CRFJsonValue = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CRFTextData = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    AIExtractJsonValue = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TotalField = table.Column<int>(type: "int", nullable: false),
                    FillField = table.Column<int>(type: "int", nullable: false),
                    Remark = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: false),
                    CreateUserName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    CreatedTime = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AITaskCRFDatas", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AITaskCRFDatas_AITaskTypes_AITaskTypeId",
                        column: x => x.AITaskTypeId,
                        principalTable: "AITaskTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_AITaskCRFDatas_FollowupRecords_FollowupRecordId",
                        column: x => x.FollowupRecordId,
                        principalTable: "FollowupRecords",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_AITaskCRFDatas_ResearchPatients_PatientId",
                        column: x => x.PatientId,
                        principalTable: "ResearchPatients",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "AITaskPrompts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PropmtPoint = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    PropmtName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    ModelName = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    HospitalDeptId = table.Column<int>(type: "int", nullable: true),
                    AITaskTypeId = table.Column<int>(type: "int", nullable: false),
                    Prompt = table.Column<string>(type: "nvarchar(4000)", maxLength: 4000, nullable: false),
                    Note = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: false),
                    IsUsed = table.Column<bool>(type: "bit", nullable: false),
                    CreateUser = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    CreatedTime = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AITaskPrompts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AITaskPrompts_AITaskTypes_AITaskTypeId",
                        column: x => x.AITaskTypeId,
                        principalTable: "AITaskTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_AITaskPrompts_HospitalDepts_HospitalDeptId",
                        column: x => x.HospitalDeptId,
                        principalTable: "HospitalDepts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRFormFieldSets",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FormId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    FormName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    FieldName = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    FieldComment = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    Orderby = table.Column<int>(type: "int", nullable: false),
                    ExtractSet = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    MinRangeValue = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    MaxRangeValue = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    CreateUserName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    CreatedTime = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRFormFieldSets", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRForms",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    HospitalDeptId = table.Column<int>(type: "int", nullable: true),
                    AITaskTypeId = table.Column<int>(type: "int", nullable: false),
                    FormId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    FormName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    OrderBy = table.Column<int>(type: "int", nullable: false),
                    StopUsing = table.Column<bool>(type: "bit", nullable: false),
                    Remark = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    CreateUserName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    CreatedTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    BusinessDomain = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRForms", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRForms_AITaskTypes_AITaskTypeId",
                        column: x => x.AITaskTypeId,
                        principalTable: "AITaskTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CRForms_HospitalDepts_HospitalDeptId",
                        column: x => x.HospitalDeptId,
                        principalTable: "HospitalDepts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AITaskCRFDatas_AITaskTypeId",
                table: "AITaskCRFDatas",
                column: "AITaskTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_AITaskCRFDatas_FollowupRecordId",
                table: "AITaskCRFDatas",
                column: "FollowupRecordId");

            migrationBuilder.CreateIndex(
                name: "IX_AITaskCRFDatas_PatientId",
                table: "AITaskCRFDatas",
                column: "PatientId");

            migrationBuilder.CreateIndex(
                name: "IX_AITaskPrompts_AITaskTypeId",
                table: "AITaskPrompts",
                column: "AITaskTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_AITaskPrompts_HospitalDeptId_AITaskTypeId_PropmtPoint_ModelName",
                table: "AITaskPrompts",
                columns: new[] { "HospitalDeptId", "AITaskTypeId", "PropmtPoint", "ModelName" },
                unique: true,
                filter: "[HospitalDeptId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_CRForms_AITaskTypeId",
                table: "CRForms",
                column: "AITaskTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_CRForms_FormId_HospitalDeptId_AITaskTypeId",
                table: "CRForms",
                columns: new[] { "FormId", "HospitalDeptId", "AITaskTypeId" },
                unique: true,
                filter: "[HospitalDeptId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_CRForms_HospitalDeptId",
                table: "CRForms",
                column: "HospitalDeptId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AITaskCRFDatas");

            migrationBuilder.DropTable(
                name: "AITaskPrompts");

            migrationBuilder.DropTable(
                name: "CRFormFieldSets");

            migrationBuilder.DropTable(
                name: "CRForms");
        }
    }
}
