﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AngelwinFollowUp.Models
{
    public partial class FollowupTemplate
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string TemplateName { get; set; } = string.Empty; // 模板名称

        [Required]
        [MaxLength(50)]
        public string DiseaseCode { get; set; } = string.Empty;  // 疾病/手术编码

        [DefaultValue(0)]
        public int Followup { get; set; }  // 随访次数

        [MaxLength(2000)]
        public string? Remark { get; set; } // 备注

        [MaxLength(100)]
        public string CreateUserName { get; set; } = string.Empty;  // 创建人

        public DateTime CreatedTime { get; set; } = System.DateTime.Now;  // 创建时间

        [ForeignKey("AITaskType")]
        public int AITaskTypeId { get; set; } // 外键，关联 AITaskType 表

        public virtual AITaskType AITaskType { get; set; } = null!;


        public ICollection<FollowupTemplateDetail> FollowupTemplateDetails { get; } = new List<FollowupTemplateDetail>();
    }
}
