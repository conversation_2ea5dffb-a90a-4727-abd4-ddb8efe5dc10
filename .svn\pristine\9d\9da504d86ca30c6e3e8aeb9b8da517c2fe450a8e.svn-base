﻿@{
    ViewBag.Title = "主页";
    Layout = null;
}
@model List<MenuTreeDTO>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title> @ViewBag.SiteTitle</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/layuiadmin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/layuiadmin/style/admin.css" rel="stylesheet" />
    <script src="~/js/jquery-3.5.1.min.js"></script>
</head>
<body class="layui-layout-body">
    <div id="LAY_app">
        <div class="layui-layout layui-layout-admin">
            <div class="layui-header">
                <!-- 头部区域 -->
                <ul class="layui-nav layui-layout-left">
                    <li class="layui-nav-item layadmin-flexible" lay-unselect>
                        <a href="javascript:;" layadmin-event="flexible" title="侧边伸缩">
                            <i class="layui-icon layui-icon-shrink-right" id="LAY_app_flexible"></i>
                        </a>
                    </li>
                    <li class="layui-nav-item" lay-unselect>
                        <a href="javascript:;" layadmin-event="refresh" title="刷新">
                            <i class="layui-icon layui-icon-refresh-3"></i>
                        </a>
                    </li>
                </ul>
                <ul class="layui-nav layui-layout-right" lay-filter="layadmin-layout-right">
                    <li class="layui-nav-item layui-hide-xs" lay-unselect>
                        <a href="javascript:;" layadmin-event="theme">
                            <i class="layui-icon layui-icon-theme"></i>
                        </a>
                    </li>
                    <li class="layui-nav-item layui-hide-xs" lay-unselect>
                        <a href="javascript:;" layadmin-event="fullscreen">
                            <i class="layui-icon layui-icon-screen-full"></i>
                        </a>
                    </li>
                    <li class="layui-nav-item" lay-unselect>
                        <a href="javascript:;">
                            <cite id="WorkId">@ViewBag.trueName</cite>
                        </a>
                        @if (ViewBag.LoginType != "Single")
                        {
                            <dl class="layui-nav-child">
                                <dd><a lay-href="/Account/ResetPwd">修改密码</a></dd>
                                <dd><a lay-href="/Home/changelog">更新日志</a></dd>
                                <hr />
                                <dd style="text-align: center;"><a href="#" id="signOut">退出</a></dd>
                            </dl>
                        }

                    </li>
                </ul>
            </div>

            <!-- 侧边菜单 -->
            <div class="layui-side layui-side-menu">
                <div class="layui-side-scroll">
                    <div class="layui-logo">
                        <img src="/images/hospitallogo1.png" style="width:190px;" />
                        @*@ViewBag.SiteTitle*@
                    </div>
                    <ul class="layui-nav layui-nav-tree" lay-shrink="all" id="LAY-system-side-menu" lay-filter="layadmin-system-side-menu">
                        @{
                            foreach (var treeDTO in Model)
                            {

                                <li data-name="@treeDTO.MenuID" class="layui-nav-item">
                                    <a href="javascript:;" lay-tips="@treeDTO.MenuName" lay-direction="2">
                                        <i class="layui-icon @treeDTO.MenuIcon"></i>
                                        <cite>@treeDTO.MenuName</cite>
                                    </a>
                                    <dl class="layui-nav-child">
                                        <partial name="MenuHtmlPartialChild" model="@(new MenuPartialDTO
                                                                             {
                                                                                 MenuTreeDTOList = treeDTO.Children,
                                                                                 Ticket = ViewBag.Ticket
                                                                             })" />
                            </dl>

                                </li>
                                                }
                        }

                    </ul>
                </div>
            </div>

            <!-- 页面标签 -->
            <div class="layadmin-pagetabs" id="LAY_app_tabs">
                <div class="layui-icon layadmin-tabs-control layui-icon-prev" layadmin-event="leftPage"></div>
                <div class="layui-icon layadmin-tabs-control layui-icon-next" layadmin-event="rightPage"></div>
                <div class="layui-icon layadmin-tabs-control layui-icon-down">
                    <ul class="layui-nav layadmin-tabs-select" lay-filter="layadmin-pagetabs-nav">
                        <li class="layui-nav-item" lay-unselect>
                            <a href="javascript:;"></a>
                            <dl class="layui-nav-child layui-anim-fadein">
                                <dd layadmin-event="closeThisTabs"><a href="javascript:;">关闭当前标签页</a></dd>
                                <dd layadmin-event="closeOtherTabs"><a href="javascript:;">关闭其它标签页</a></dd>
                                <dd layadmin-event="closeAllTabs"><a href="javascript:;">关闭全部标签页</a></dd>
                            </dl>
                        </li>
                    </ul>
                </div>
                <div class="layui-tab" lay-unauto lay-allowClose="true" lay-filter="layadmin-layout-tabs">
                    <ul class="layui-tab-title" id="LAY_app_tabsheader">
                        <li lay-id="/home/<USER>" lay-attr="/home/<USER>" class="layui-this"><i class="layui-icon layui-icon-home"></i></li>
                    </ul>
                </div>
            </div>


            <!-- 主体内容 -->
            <div class="layui-body" id="LAY_app_body">
                <div class="layadmin-tabsbody-item layui-show">
                    <iframe src="/Home/WelCome" frameborder="0" class="layadmin-iframe"></iframe>
                </div>
            </div>

            <!-- 辅助元素，一般用于移动设备下遮罩 -->
            <div class="layadmin-body-shade" layadmin-event="shade"></div>
        </div>

    </div>
    <script src="~/layuiadmin/layui/layui.js"></script>
    <script>
        layui.config({
            base: '/layuiadmin/' //静态资源所在路径
            , verson: true
        }).extend({
            index: 'lib/index' //主入口模块
        }).use(['index', 'common', 'laytpl', 'element'], function () {
            var $ = layui.$;
            $("#signOut").click(function () {
                signOut();
            });

            $(".layui-side-scroll li a").mouseover(function () {
                $(this).addClass("layui-this");
            }).mouseout(function () {
                $(this).removeClass("layui-this");
            });
            function signOut() {
                window.location.href = "/Account/LogOff";
            }
        });

    </script>

</body>
</html>