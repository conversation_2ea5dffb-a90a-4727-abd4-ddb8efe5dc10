﻿using AngelwinFollowUp.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AngelwinFollowUp.Models
{
    /// <summary>
    /// CRF表单管理 addbyzolf 20250709
    /// 跟科研AI相比，删除了层级和父键关闭，科研AI tabletree的方式，表单多了后不好分页，完全采用表格形式展示；
    /// </summary>
    public partial class CRForm
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [ForeignKey("HospitalDept")]
        public int? HospitalDeptId { get; set; } //所属机构
        public virtual HospitalDept HospitalDept { get; set; } = null!;

        [ForeignKey("AITaskType")]
        public int AITaskTypeId { get; set; } //外键，关联 AITaskType 表
        public virtual AITaskType AITaskType { get; set; } = null!;

        [Required]
        [MaxLength(100)]
        public string FormId { get; set; } = null!;

        [Required]
        [MaxLength(200)]
        public string FormName { get; set; } = null!;//表单名称

        [DefaultValue(0)]
        public int OrderBy { get; set; }

        public bool StopUsing { get; set; }//启用、停用开关    false/0:停用；    true/1:启用；

        [MaxLength(1000)]
        public string Remark { get; set; } = null!;

        [MaxLength(50)]
        public string CreateUserName { get; set; } = null!;
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 业务域(从基础字典中获取：基本信息、病历文书、检验、检查)
        /// </summary>
        [MaxLength(500)]
        public string BusinessDomain { get; set; } = null!; 
    }
}
