﻿@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>随访患者查询</title>

    <link href="~/layuiadmin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/layuiadmin/style/admin.css" rel="stylesheet" />
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <style>
        /*弹窗*/
        .window_wrap {
            padding: 15px;
        }

        .search_wrap {
            background-color: #fff;
            display: flex;
            flex-direction: row;
            padding: 10px 4px;
        }

        #detail_window .layui-form-label {
            width: 100px;
        }

        #detail_window .layui-form-val {
            padding: 9px 15px;
        }

        #detail_window .mindow_icon .title_icon {
            display: inline-block;
        }

        #detail_window .mindow_icon {
            text-align: center;
            padding: 20px 0;
        }

        #detail_window .layui-form-item {
            margin-bottom: 0;
        }

        .layui-form-label {
            width: 100px;
        }

        .layui-input-block {
            margin-left: 140px;
            padding-right: 15px;
        }

        .line_wrap {
            display: flex;
            flex-direction: row;
            padding-right: 15px;
        }


        .layui-form-item .layui-input-inline {
            width: 50%;
            margin-right: 0;
            margin-left: 10px;
        }
        /* 定义表头样式 */
        .layui-table th {
            font-weight: bold; /* 加粗 */
            font-size: 14px; /* 调整字体大小 */
            color: #333; /* 字体颜色，可根据需要调整 */
        }

        .form_item {
            padding-top: 10px;
        }
    </style>
</head>
<body style="padding:5px;">

    <div class="layui-col-md12">

        <div class="layui-card">

            <div class="layui-card-body layui-form search_wrap">
             
                <div class="layui-inline" style="width: 400px;">
                    <div class ="form_item">
                        <label class="layui-form-label">患者来源</label>
                        <div class="layui-input-block ">
                            <select name="PatientSource" id="PatientSource" lay-search="">
                                <option value="" selected="selected">全部</option>
                                <option value="I" >门诊</option>
                                <option value="O">住院</option>
                                <option value="P">体检</option>
                            </select>
                        </div>
                      
                    </div>
                 
                </div>


                <div class="layui-inline" style="width: 400px;display:none" >
                    <div class="form_item">

                        <label class="layui-form-label">随访人</label>
                        <div class="layui-input-block ">

                            <input type="text" class="layui-input" name="PatientName" id="PatientName" />
                        </div>
                       
                    </div>
                  
                </div>

                <div class="layui-inline" style="width: 400px;">
                    <div class="form_item">

                        <label class="layui-form-label">随访状态</label>
                        <div class="layui-input-block ">
                            <select name="Status" id="Status" lay-search="">
                                <option value="" selected="selected">全部</option>
                                <option value="0" >待随访</option>
                                <option value="1">已随访</option>
                                <option value="-1">失访</option>
                            </select>
                        </div>
                       
                    </div>
                   
                </div>
                <div class="layui-inline" style="width:500px;">
                     
                    <div class ="form_item">

                        <label class="layui-form-label">随访时间</label>
                        <div class="layui-input-block ">

                            <input type="text" name="searchDate" id="searchDate" placeholder="yyyy-MM-dd~yyyy-MM-dd" autocomplete="off" class="layui-input">


                        </div>
                      
                    </div>
                  
                </div>
                <div class="layui-inline" style="width:500px;">
                 
                    <div class="form_item">

                        <label class="layui-form-label">关键字</label>
                        <div class ="layui-input-block ">

                            <input type="text" class="layui-input" placeholder="请输入手机号,ID号" name="KeyWords" id="KeyWords" />
                        </div>
                       
                    </div>
                   
                </div>
                <div class="layui-inline" style="margin-left: 10px;">
                    <div class="form_item">
                        <div class="layui-input-block ">
                            <button class="layui-btn layui-btn-primary fr" id="Search"><i class="layui-icon layui-icon-search"></i></button>
                        </div>
                    
                    </div>
                    
                 
                </div>
            </div>


            <div class="layui-card-body">

                <table class="layui-table layui-form" id="tablelist" lay-filter="tablelist"></table>
                <script type="text/html" id="tableBar">
                   
                 
                 @*    <a class="layui-btn  layui-btn-xs" lay-event="show"><i class="layui-icon layui-icon-table"></i>查看明细</a> *@

                    <a class="layui-btn  layui-btn-xs" lay-event="AI">AI随访</a>
                    <a class="layui-btn  layui-btn-xs" lay-event="AIAnalysis">随访分析</a>
                </script>
            </div>

        </div>
 
    </div>

    <script type="text/html" id="switchTpl">
        <!-- 这里的 checked 的状态只是演示 -->
        <input type="checkbox" id="switch" name="{{d.Id}}" value="{{d.StopUsing}}" lay-skin="switch" lay-text="停用|启用" lay-filter="show" {{ d.StopUsing == 'on' ? 'checked' : '' }}>
    </script>

    <script src="~/layuiadmin/layui/layui.js"></script>
    <script src="~/layuiadmin/layuiextend/xm-select.js"></script>
    <script src="~/js/jquery-3.5.1.min.js"></script>

    <script>

           layui.config({
            base: '/layuiadmin/layuiextend/'
           }).use(['element', 'layer', 'table', 'laydate', 'form','jquery','laydate'], function() {
             
               var table = layui.table//表格
                  , laydate = layui.laydate
                  , layer = layui.layer
                   , form = layui.form
                  , $ = layui.$
                  ;


               var beginDateStr = '@ViewBag.BeginDate';
              var endDateStr = '@ViewBag.endDate';
              var start = laydate.render({
                  elem: '#searchDate' //指定元素
                  , range: '~'
                 // , value: beginDateStr + ' ~ ' + endDateStr
                  , format: 'yyyy-MM-dd'
                  , btns: ['confirm']
                  , done: function (value, date, endDate) {
                      console.log(value);
                     beginDateStr = value.split('~')[0];
                     endDateStr = value.split('~')[1];
                  }
              });

              $("#searchDate").blur(function () {

                  start.config.min = {
                      year: '1900',
                      month: '0',//关键
                      date: '1',
                  };
                  start.config.max = {
                      year: '2100',
                      month: '0',//关键
                      date: '1',
                  };

              });

             table.render({
              elem: '#tabledetail', 
              height: 'full-155',
              page: false,//开启分页
              cols: [
                  [   {
                        field: 'TemplateDetailName',
                        title: '模板明细名称',
                          width: 150
                      }, {
                        field: 'Methods',
                        title: '随访方式',
                          width: 100,
                          
                      },
                     {
                    field: 'CreatedTime',
                      title: '随访日期',
                        width: 120,

                    },
                   {
                   field: 'Status',
                    title: '随访状态',
                      width: 100,

                  },
                      {
                        field: 'Description',
                        title: '随访内容',
                         width: 1000 
   
                      } ]

              ],
              done: function () {
                  $("tabledetail").css("width", "100%");
              }
          });

              table.render({
                  elem: '#tablelist'
                  , id: 'tablelist'
                  , page: true

                  , limit:20
                  , height: 'full-125'
                  , cols: [[
                      { field: 'No', title: '序号', type: 'numbers', fixed: 'left' }
                 , { field: 'TemplateName', title: '模板名', width: 150 }
                 , { field: 'IDCardNo', title: '卡号', width: 150 }
                    , { field: 'PatientName', title: '患者姓名', width: 150 }
                    , { field: 'Sex', title: '性别' }
                     , { field: 'Age', title: '年龄' }
                   , { field: 'DeptName', title: '科室', width:100 }
                 , { field: 'FollowupCounts', title: '随访次数',width: 100, templet: function (d) {

                 return '  <a href="javascript:void(0);" class="layui-btn layui-btn-xs" onclick="ShowTableDetail( \''+d.TemplateDetailId+'\', \'' +d.PatientName+ '\')"><span>' + d.FollowupCounts + '</span> </a>';
                    }}
                 , { field: 'PatientSource', title: '来源',width: 80 }
                 // , { field: 'StatusTime', title: '随访日期',width: 160 }
                  , { field: 'CreatedTime', title: '创建日期',width: 160 }
                  // , { field: 'Status', title: '状态' ,width: 150}
                 , { field: 'Diagnosis', title: '诊断',width: 260}
           
                 // , { field: 'Description', title: '标签',width: 160}
                    , { title: '操作', toolbar: '#tableBar', width: 150, minWidth:150, fixed: 'right' }
                  ]]
                  , done: function (res, curr, count) {
                  }
              });
                  //监听tablelist工具条
             table.on('tool(tablelist)', function (obj) {
                  var data = obj.data;
                  if (obj.event === 'show') {
                     table.reload('tabledetail', {
                        url: '/FollowUpManage/FollowUpManageSearch/GetFollowupTemplateDetailsList/' //数据接口
                        , where: { 'ids': data.TemplateDetailId }

                      });
                      layer.open({
                          type: 1
                        , title: '查看【' + data.PatientName + '】的随访明细'
                        , content: $("#FollowupTemplateDetails")
                          , maxmin: true
                          , area: ['70%', '90%']
                      });
                  }

                    if(obj.event === 'AI'){

                      var url='/AIFollowUp/FollowUpAI/Index?patientId='+data.PId+"&templateId="+data.TemplateId;
             
                      parent.layui.index.openTabsPage(url, "AI随访");
                    }

                  if(obj.event === 'AIAnalysis'){

                   var url='/AIFollowUp/FollowupSummary/Index?patientId='+ data.PId+"&templateId="+data.TemplateId;

                       parent.layui.index.openTabsPage(url, "随访分析");
                  }
              });

              $(document).ready(function () {

                 SearchData();
                 $(document).on('click', '#Search', function () {
             
                    SearchData();
                 })

               

              });

               function SearchData() {

                      table.reload('tablelist', {
                          page: {
                              curr: 1
                          },
                         url: '/FollowUpManage/FollowUpManageSearch/GetFollowupList'
                          , where: {
                               'PatientName':$.trim($("#PatientName").val()),
                               'PatientSource':$.trim($("#PatientSource").val()),
                               'Status':$.trim($("#Status").val()),
                              'KeyWords': $.trim($("#keyWords").val()) ,
                              'BeginDate': beginDateStr,
                              'endDate': endDateStr

                          }
                      });
          };

            

              function setTableH() {
              var winH = $(window).height();
              var navH = $(".layui-tab-brief").height();
              var searchH = $(".search_wrap").height();
              var editAreaH = $(".edit_area").height();
              var tableH = winH - (navH + searchH + editAreaH) - 55 + "px";
              $(".table_wrap").css("height", tableH);

          };
              setTableH();
               $(window).resize(function () {
              setTableH()
              });

          

          });

         function ShowTableDetail( id,PatientName){

                console.log(id);
               layui.table.reload('tabledetail', {
                        url: '/FollowUpManage/FollowUpManageSearch/GetFollowupTemplateDetailsList/' //数据接口
                        , where: { 'ids': id }

                 });
              layui.layer.open({
                    type: 1
                   ,shade: 0
                , title: '查看【' +  PatientName + '】的随访明细'
                , content: $("#FollowupTemplateDetails")
                    , maxmin: true
                    , area: ['70%', '90%']
                });
           }
    </script>
</body>
<div class="layui-form" lay-filter="FollowupTemplateDetails" id="FollowupTemplateDetails" style="padding: 20px 30px 0 0; display :none ">
    <div class="layui-card">
        <div class="layui-card-body">	<table id="tabledetail" lay-filter="tabledetail"></table></div>

    </div>


</div>

</html>
