﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AngelwinFollowUp.Models
{
    public partial class FollowupTemplateDetail
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string TemplateDetailName { get; set; } = string.Empty; // 模板明细名称

        [Required]
        [MaxLength(50)]
        public  string Methods { get; set; } = string.Empty;// 随访方式

        public string Description { get; set; } = string.Empty; // 随访内容

        public int Orderby { get; set; } // 随访点

        [MaxLength(50)]
        public string? FollowupPoint { get; set; } // 随访时间点

        [DefaultValue(0)]
        public int DelayDay { get; set; } // 正负天数

        [MaxLength(2000)]
        public string? Remark { get; set; } // 备注

        [MaxLength(100)]
        public string CreateUserName { get; set; } = string.Empty; // 创建人

        public DateTime CreatedTime { get; set; } = System.DateTime.Now;  // 创建时间

        [Required]
        [ForeignKey("FollowupTemplate")]
        public int TemplateId { get; set; } // 外键，关联 FollowupTemplates 表

        public virtual FollowupTemplate FollowupTemplate { get; set; } = null!;

    }
}
