﻿@{
    ViewBag.Title = "跟进总结";
    Layout = null;
}

@model AngelwinFollowUp.Models.ResearchPatient
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title> @ViewBag.SiteTitle</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/layuiadmin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/layuiadmin/style/admin.css" rel="stylesheet" />
    <script src="~/js/jquery-3.5.1.min.js"></script>
    <script src="~/layuiadmin/layui/layui.js"></script>
    <script src="~/js/marked.min.js"></script>
    <style>
        .layui-panel {
            background-color: #fff;
            padding: 20px;
        }

        .patient_info {
            border-bottom: 1px solid #eee;
        }

        .flex-row {
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        .flex_wrap {
            flex-wrap: wrap;
        }

        .user_name {
            min-width: 120px;
            line-height: 30px;
            font-size: 18px;
            font-weight: bold;
        }

        .sex {
            font-size: 14px;
            font-weight: normal;
        }

        .sex0 {
            color: #FF5722;
        }

        .sex1 {
            color: #1E9FFF;
        }

        .user_value {
            color: #000;
        }

        .info_item {
            padding: 6px 0;
            padding-left: 15px;
        }

        .content_wrap {
            margin: 6px 0;
        }

        .list_wrap {
            height: 90vh;
            background-color: #F9FBFF;
            padding: 1px;
            justify-content: space-between;
        }

        .Follow-up_content {
            height: 89vh;
            padding: 5px;
            background-color: #fff;
        }


        /* 随访列表 */
        .Follow-up_list {
            height: 84vh;
            overflow-y: auto;
        }

        .list_title {
            line-height: 50px;
            padding: 0 16px;
            font-size: 16px;
            justify-content: space-between;
        }

        .active {
            background: linear-gradient(70deg, #dcffff, #62ffff);
            border-color: transparent;
            color: #1E9FFF;
        }

        .nav_item {
            border-top-color: rgb(238, 238, 238);
            border-right-color: rgb(238, 238, 238);
            border-bottom-color: rgb(238, 238, 238);
            border-left-color: rgb(238, 238, 238);
            padding: 10px 15px;
            border-radius: 4px;
            margin: 0 5px 10px;
        }

        .title_wrap {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }

        .micro_btn {
            width: 120px;
            height: 30px;
            flex: 1;
        }

            .micro_btn .layui-btn {
                width: 120px;
                height: 44px;
                line-height: 44px;
                margin: 0;
                border-radius: 6px;
            }

        .type0 {
            color: #c2c2c2;
        }

        .type1 {
            color: #1E9FFF;
        }

        .layui-tab {
            margin: 0;
        }

        .layui-tab-content {
            background-color: #fff;
        }

        .layui-tab-item {
            height: 100%;
        }

        .selector {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            margin-bottom: 10px;
        }

        /* 特定于助手的回答 */
        .answer {
            display: flex;
            flex-direction: row;
        }

        .summarywrap {
            /*  padding:12px;
                    margin-top:10px; */
            height: 90%;
            overflow-y: auto;
            border: 1px solid #d2d2d2;
        }

        .summary_content {
            flex: 1;
        }

        #summaryContent {
            font: 16px Helvetica Neue, Helvetica, PingFang SC, Tahoma, Arial, sans-serif;
        }

            #summaryContent h3, h4 {
                margin: 0px 20px 0px 20px;
                padding: 2px 2px 0px 0px;
            }

            #summaryContent ul {
                margin: 18px 0px 18px 0px;
                padding: 0px 0px 0px 40px;
            }

            #summaryContent p {
                margin: 16px 0px 16px 0px;
                padding: 0px 0px 0px 40px;
            }

        .answer .logo_pic_container {
            float: left;
            margin-right: 10px; /* 添加一些间距 */
        }

        .answer_inner {
            flex-grow: 1;
            padding: 10px;
        }

        .logo_pic {
            flex-grow: 0;
            flex-shrink: 0;
            width: 40px;
            height: 40px;
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid #eee;
            margin-right: 10px;
            border-radius: 50%;
        }

            .logo_pic img {
                width: 100%;
            }

        .text_wrap {
            max-width: 100%;
            background-color: #eff6ff;
            padding: 10px;
            margin-right: 10px;
            border-radius: 0px 6px 6px 6px;
        }

        /*  .item_left {
                    flex: 1;
                }

                .title_wrap.flex-row {
                    justify-content: space-between;
                    align-items: center;
                }

                .nav_item:hover {
                    background-color: #f2f2f2;
                    cursor: pointer;
                } */

    </style>


</head>
<body class="layui-layout-body  layui-form">
    <div class="layui-panel flex-row userwrap">
        <input type="hidden" id="hiddenPatientId" />
        <div class="user_name">@Model.PatientName <span class="sex sex0">@Model.Sex</span></div>
        <div class="user_info flex-row flex_wrap">
            <div class="info_item">编号：<span class="user_value">@Model.PatientId</span></div>
            <div class="info_item">科室：<span class="user_value">@Model.HospitalDept.Name</span></div>
            <div class="info_item">临床诊断：<span class="user_value">@Model.Diagnosis</span></div>
        </div>
    </div>

    <div class="content_wrap layui-row">
        <div class="layui-col-xs2">
            <div class="list_wrap">
                <div class="list_wrap">
                    <div class="list_title flex-row">
                        <p>
@(ViewBag.TypeName)记录</p>
                        <button class="layui-btn layui-btn-xs" id="showFollow">查看@(ViewBag.TypeName)</button>
                    </div>
                    <ul class="Follow-up_list" lay-filter="followupList" id="followupList">
                        @* <li class="nav_item">
                            <div class="title_wrap flex-row">
                                <div class="item_left">
                                    <input type="checkbox" name="" title="第一次随访" lay-skin="primary" checked>
                                </div>
                                <div><span class="type1">已完成</span></div>
                            </div>
                        </li>
                        <li class="nav_item">
                            <div class="title_wrap flex-row">
                                <div class="item_left">
                                    <input type="checkbox" name="" title="第二次随访" lay-skin="primary" >
                                </div>
                                <div><span class="type0">未完成</span></div>
                            </div>
                        </li>
                        <li class="nav_item">
                            <div class="title_wrap flex-row">
                                <div class="item_left">
                                    <input type="checkbox" name="" title="第三次随访" lay-skin="primary" >
                                </div>
                                <div><span class="type0">未完成</span></div>
                            </div>
                        </li> *@
                    </ul>
                </div>
            </div>
        </div>
        <div class="layui-col-xs10">
            <div class="layui-tab layui-tab-card layui-form">
                <ul class="layui-tab-title">
                    <li class="layui-this">@(ViewBag.TypeName)分析</li>
                    <li>@(ViewBag.TypeName)特征变量提取</li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show">
                        <div class="selector ">
                            <button type="button" class="layui-btn layui-bg-blue" id="btnSummary">一键总结</button>

                        </div>
                        <div class="summarywrap">
                            <div id="summaryContainer"></div>
                            @* <div class="summary_item answer">
                                <div class="logo_pic_container"><img src="/images/caduceus.png" class="logo_pic"></div>
                                <div class="summary_content">
                                    <p>AI报告解读</p>
                                    <div class=" text_wrap">
                                        <p>根据您的随访内容，以下是解读报告</p>
                                        <p><strong>患者主诉</strong></p>
                                        <p><strong>当前状况</strong></p>
                                        <p><strong>存在的问题</strong></p>
                                        <p><strong>医生建议</strong></p>
                                    </div>
                                </div>
                            </div> *@
                        </div>
                    </div>
                    <div class="layui-tab-item">
                        <div class="selector ">
                            <div class="layui-inline">
                                <label class="layui-form-label">@(ViewBag.TypeName)内容</label>
                                <div class="layui-input-inline">
                                    <select name="quiz" id="formTypeSelect" lay-filter="quiz">
                                        <option value="">请选择</option>
                                        <option value="1">患者随访记录表</option>
                                        <option value="2">帕金森病问诊记录</option>
                                    </select>
                                </div>
                                <button type="button" class="layui-btn layui-bg-blue" id="btnExtract">特征变量提取</button>
                                <button class="layui-btn fr" id="download">下 &nbsp;&nbsp;载</button>
                            </div>
                        </div>
                        <iframe id="reportingFrom"
                                width="100%"
                                height="93%"
                                frameborder="1"
                                scrolling="yes"
                                style="border:1px solid #d2d2d2">
                        </iframe>
                        <input type="hidden" id="AIExtractJsonValue" />
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>

           layui.config({
            base: '/layuiadmin/'
        }).use(['layer', 'form','element', 'code', 'laytpl',], function(){
            var layer = layui.layer,
                $ = layui.$,
                 laytpl = layui.laytpl,
                 element = layui.element,
                 form = layui.form;
                 var templateId = @ViewBag.TemplateId;
                 var patientId = @Model.Id;
                 var typeId=@ViewBag.TypeId;

                 // 表单类型配置映射
                 var formTypeConfig = {
                     "1": {
                         name: "结肠癌随访记录",
                         CRFormId: "2aff55508cd9453bb4de6e5ab76d1369",
                         formId: "2aff55508cd9453bb4de6e5ab76d1369"
                     },
                     "2": {
                         name: "帕金森病问诊记录",
                         CRFormId: "ebe16dac613c467c8149dad7496ad50b",
                         formId: "ebe16dac613c467c8149dad7496ad50b"
                     }
                 };

                 // 根据表单类型获取配置
                 function getFormConfig(formType) {
                     return formTypeConfig[formType] || null;
                 }

                 // 加载表单iframe
                 function loadFormIframe(formType) {
                     var formConfig = getFormConfig(formType);
                     if (!formConfig) {
                         layer.msg('请选择有效的随访内容类型');
                         return;
                     }

                     var formId = formConfig.formId;
                     console.log(formId);
                     // 显示iframe
                     $('#reportingFrom').show();
                     var randVersion = Math.random();
                     var url = "@($"{ViewBag.formUrl}")" + formId + "@($".form?token={ViewBag.token}")&version=" + randVersion;
                     $("#reportingFrom").attr("src", url);

                     var DelayTime = parseInt('2000', 10);
                     setTimeout(function() {
                         loadData();
                     }, DelayTime);
                 }

                 $(document).ready(function () {
                     $('select[name="quiz"] option:eq(1)').prop('selected', true);

                    getFollowupRecords(patientId,templateId); //获取患者随访记录

                    // 监听表单类型选择变化 - 使用Layui的form监听
                    form.on('select(quiz)', function(data){
                        var selectedFormType = data.value;
                        console.log('表单变化监听(Layui)：' + selectedFormType);
                        if (selectedFormType) {
                            var currentTabIndex = $('.layui-tab-title .layui-this').index();
                            if (currentTabIndex === 1) {
                                loadFormIframe(selectedFormType);
                            }
                        }
                    });
                 });

                $(window).resize(function () {
                    setContentH()
                });

                function setContentH(){
                    var list_wrapH = $(".list_wrap").height();
                    var layuitabtitleH = $(".layui-tab-title").height();
                    var ContentH = list_wrapH-layuitabtitleH-20+"px";
                    $(".layui-tab-content").css("height",ContentH);
                };
                setContentH();

                  // 获取随访记录列表
                 function getFollowupRecords(patientId,templateId) {
                    $.ajax({
                        url: '/AIFollowUp/FollowUpSummary/GetFollowupRecords?patientId=' + patientId + '&templateId=' + templateId,
                        type: 'GET',
                        success: function (data) {
                            console.log(data);
                            var followupList = $("#followupList");
                            followupList.empty();
                            $.each(data, function (index, item) {
                             // 根据状态设置样式类型
                            var statusClass = item.status === 1 ? 'type1' : 'type0';
                            var statusText = item.status === 1 ? '已完成' : '未完成';

                            var listItem = `
                                <li class="nav_item">
                                    <div class="title_wrap flex-row">
                                        <div class="item_left">
                                            <input type="checkbox"
                                                   lay-skin="primary"
                                                   name="followup_${item.id}"
                                                   title="${item.templateDetailName || '@(ViewBag.TypeName)记录'}"
                                                   data-recordid="${item.recordId || ''}"
                                                   data-id="${item.id}"
                                                   data-patientid="${item.patientId}"
                                                   data-planid="${item.id}"
                                                   data-templatedetailid="${item.templateDetailId}">
                                        </div>
                                        <div>
                                            <span class="${statusClass}">${statusText}</span>
                                        </div>
                                    </div>
                                </li>`;

                            followupList.append(listItem);
                            });
                          form.render();
                        },
                        error: function (xhr, status, error) {
                            layer.alert("获取@(ViewBag.TypeName)记录失败：" + error);
                        }
                    });
                 }

                  // 监听一键总结按钮点击
                 $('#btnSummary').on('click', function() {
                // 获取选中的随访记录ID
                var selectedFollowupIds = [];
                $('.Follow-up_list input[type="checkbox"]:checked').each(function() {
                    selectedFollowupIds.push($(this).data('recordid'));
                });

                if (selectedFollowupIds.length === 0) {
                    layer.msg('请至少选择一次@(ViewBag.TypeName)记录');
                    return;
                }
                generateSummary(selectedFollowupIds);
            });

                 function generateSummary(selectedFollowupIds) {
            // 创建初始HTML结构
            const summaryHtml = `
                <div class="summary_item answer">
                    <div class="logo_pic_container">
                        <div class="logo_pic">
                            <img src="@Url.Content("~/images/caduceus.png")" alt="AI">
                        </div>
                    </div>
                    <div class="summary_content">
                        <p>根据您选择的${selectedFollowupIds.length}次@(ViewBag.TypeName)记录，以下是综合分析报告：</p>
                        <div id="summaryContent"></div>
                    </div>
                </div>`;

            $('#summaryContainer').html(summaryHtml);


            let loadIndex = layer.load(1, {
                shade: [0.1, '#fff']
            });

            // 关闭已存在的SSE连接
            if (window.currentEventSource) {
                window.currentEventSource.close();
            }

                // 创建SSE连接
                const eventSource = new EventSource(`/AIFollowUp/FollowUpSummary/GenerateSummary?${$.param({followupRecordIds: selectedFollowupIds}, true)}`);
                window.currentEventSource = eventSource;
                 var allData = "";
                eventSource.onmessage = function(event) {
                try {
                    const result = JSON.parse(event.data);

                    if (!result.success) {
                        layer.msg(result.message || '生成总结时发生错误');
                        eventSource.close();
                        layer.close(loadIndex);
                        return;
                    }else{
                        allData += result.data;
                        var htmlContent = marked.parse(allData);
                        $("#summaryContent").html(htmlContent);
                        // contentContainer.innerHTML += result.data;
                    }
                } catch (error) {
                    console.error('Error processing message:', error);
                    layer.msg('处理数据时发生错误');
                    eventSource.close();
                    layer.close(loadIndex);
                }
            };

            eventSource.addEventListener('end', function(event) {
                try {
                    const data = JSON.parse(event.data);
                    if (data.success) {
                        console.log('Summary generation completed');
                    }
                } catch (error) {
                    console.error('Error processing final message:', error);
                    layer.msg('处理最终数据时发生错误');
                } finally {
                    eventSource.close();
                    layer.close(loadIndex);
                }
            });

            eventSource.onerror = function(error) {
                console.error('EventSource failed:', error);
                layer.msg('连接错误');
                eventSource.close();
                layer.close(loadIndex);
            };
        }


               $("#showFollow").on("click",function(){
                var url='/AIFollowUp/FollowUpAI/Index?patientId='+patientId+'&templateId='+templateId+'&typeId='+typeId;
                parent.layui.index.openTabsPage(url, "@(ViewBag.TypeName)");
               });

                window.addEventListener('message', function(event) {
                  if(event.data.action=="show"){


                  }else if(event.data.action=="save")
                  {
                    var formData = event.data.data;
                    // 获取选择的表单类型
                    var formType = $('#formTypeSelect').val();
                    var formConfig = getFormConfig(formType);
                    if (!formConfig) {
                        layer.msg('请选择有效的@(ViewBag.TypeName)内容类型');
                        return;
                    }

                    //帕金森随访 ebe16dac613c467c8149dad7496ad50b
                    //结肠癌随访 2aff55508cd9453bb4de6e5ab76d1369
                    var CRFormId = formConfig.CRFormId;
                    var AIExtractJsonValue=$("#AIExtractJsonValue").val();
                     console.log("saveForm."+AIExtractJsonValue);
                    var obj = { CRFormId:CRFormId, CRFJsonValue: formData,AIExtractJsonValue:AIExtractJsonValue };
                    $.post('/AIFollowUp/FollowUpSummary/SaveForm', obj, function (res) {
                        if (res.code == 0) {

                            layer.msg("操作成功");
                        }
                        else
                            layer.msg("操作失败");

                    });
                  }
                }, false);


                // 监听页签点击事件 - 只处理页签切换和iframe显示
                $('.layui-tab-title li').on('click', function() {
                    var index = $(this).index();

                    // 处理tab切换
                    $(this).addClass('layui-this').siblings().removeClass('layui-this');
                    $('.layui-tab-content .layui-tab-item').eq(index).addClass('layui-show')
                        .siblings().removeClass('layui-show');
                    // "报告提取"页签
                    if(index === 1) { 
                        // 获取选中的随访记录ID
                        // var selectedFollowupIds = [];
                        // $('.Follow-up_list input[type="checkbox"]:checked').each(function() {
                        //     selectedFollowupIds.push($(this).data('recordid'));
                        // });

                        // if (selectedFollowupIds.length === 0) {
                        //     layer.msg('请至少选择一次随访记录');
                        //     return;
                        // }

                        // 获取选择的表单类型并加载对应的表单
                        var formType = $('#formTypeSelect').val();
                        if (formType) {
                            loadFormIframe(formType);
                        } else {
                            layer.msg('请选择@(ViewBag.TypeName)内容类型');
                        }
                    }
                });


              // 监听报告提取按钮点击事件
        $('#btnExtract').on('click', function() {
            // 获取选中的随访记录ID
            var selectedFollowupIds = [];
            $('.Follow-up_list input[type="checkbox"]:checked').each(function() {
                selectedFollowupIds.push($(this).data('recordid'));
            });

            if (selectedFollowupIds.length === 0) {
                layer.msg('请至少选择一次@(ViewBag.TypeName)记录');
                return;
            }

            // 获取选择的表单类型
            var formType = $('#formTypeSelect').val();
            if (!formType) {
                layer.msg('请选择@(ViewBag.TypeName)内容类型');
                return;
            }

            AIExtract(selectedFollowupIds, formType);
         });
           
            var timer;
            function AIExtract(selectedFollowupIds, formType) {
                        var loadIndex = layer.open({
                            type: 1,
                            area: ['600px', '49px'],
                            shade: 0.1,
                            closeBtn: 0,
                            resize: false,
                            title: false,
                            content: $("#form_window_load"),
                            success: function() {
                                element.progress('demo-filter-progress', '0%');
                                load();
                            }
                        });
                        $.post('/AIFollowUp/FollowUpSummary/NewAIExtract', { "patientId": patientId, "followupRecordIds": selectedFollowupIds, "formType": formType }, function(res) {

                            element.progress('demo-filter-progress', '100%');
                            clearInterval(timer);
                            if (res.code == 0) {
                                JsonObj = JSON.parse(res.data);
                                console.log("1."+res.data);
                                $("#AIExtractJsonValue").val(res.data);
                                var iframe = document.getElementById('reportingFrom');
                                // 发送消息到子页面
                                iframe.contentWindow.postMessage({ data: res.data }, "*");
                            }
                            else
                                layer.msg(res.msg);
                               layer.close(loadIndex);
                              element.progress('demo-filter-progress', '0%');


                        })
                    }

        $("#download").on("click", function () {
            bdownload();
        });

        function bdownload() {
            // 获取选择的表单类型
            var formType = $('#formTypeSelect').val();
            if (!formType) {
                layer.msg('请先选择@(ViewBag.TypeName)内容类型');
                return;
            }

            var url = "/AIFollowUp/FollowUpSummary/DownLoad?formType=" + formType;
            window.location.href = url;
         }

        //加载表单数据
        function loadData() {
            var url = "/AIFollowUp/FollowUpSummary/LoadFormData";
            $.get(url, function(res) {
                var iframe = document.getElementById('reportingFrom');
                if (res.code == 0) {
                    iframe.contentWindow.postMessage({ action: "show", data: res.data }, "*");
                }
                else {
                    // 发送消息到子页面
                    iframe.contentWindow.postMessage({ action: "show" }, "*");
                }
            })

        };

        function load() {
                var n = 0;
                timer = setInterval(function() {
                n = n + Math.random() * 10 | 0;
                if (n > 99) {
                    n = 99;
                    clearInterval(timer);

                            }
                element.progress('demo-filter-progress', n + '%');
                }, 300 + Math.random() * 4000);
            }

            })
    </script>
</body>
<div class="window_wrap" id="form_window_load" style="display: none;background-color:transparent">
    <div class="layui-progress layui-progress-big" lay-showPercent="true" lay-filter="demo-filter-progress">
        <div class="layui-progress-bar" lay-percent="0%">
        </div>

    </div>
    <p style="text-align:center"> AI数据提取中...</p>
</div>
</html>