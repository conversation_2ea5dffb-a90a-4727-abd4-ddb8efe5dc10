﻿
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using AngelwinFollowUp.Models;
using Common.Tools;
using AngelwinFollowUp.Services;
using AngelwinFollowUp.Web.Extensions;
using AngelwinFollowUp.Web.Unity;
using AngelwinFollowUp.Filters;
using AngelwinFollowUp.ModelExtends;

namespace AngelwinFollowUp.Web.Controllers
{
    public class AccountController : Controller
    {
        private readonly AngelwinFollowUpDbContext db;
        private readonly UserManager<UserInfo> UserManager;
        private readonly RoleManager<RoleInfo> RoleManager;
        private readonly SignInManager<UserInfo> SignInManager;
        private IConfiguration config { get; }
        public AccountController(AngelwinFollowUpDbContext _db, UserManager<UserInfo> _userManager, RoleManager<RoleInfo> _roleManager,
          SignInManager<UserInfo> _signInManager, IConfiguration _config)
        {
            db = _db;
            UserManager = _userManager;
            RoleManager = _roleManager;
            SignInManager = _signInManager;
            config = _config;
        }
        public IActionResult Index()
        {
            return View();
        }

        [HttpPost]
        public IActionResult test([FromBody] dynamic dynamic)
        {
            var res = new List<object>();
            res.Add(new { 日期 = "2024-01", 人数 = 5001 });
            res.Add(new { 日期 = "2024-02", 人数 = 2003 });
            res.Add(new { 日期 = "2024-03", 人数 = 6003 });
            res.Add(new { 日期 = "2024-04", 人数 = 9001 });
            res.Add(new { 日期 = "2024-05", 人数 = 3086 });
            var json = JsonConvert.SerializeObject(dynamic);
            LoggerHelper.WriteInfo("API接口", json);
            return Json(new { code = 0, msg = "操作成功", data = res });
        }

        [HttpPost]
        public IActionResult GetList([FromBody] dynamic dynamic)
        {
            var res = new List<object>();
            res.Add(new { 日期 = "2024-01", 人数 = 5001 });
            res.Add(new { 日期 = "2024-02", 人数 = 2003 });
            res.Add(new { 日期 = "2024-03", 人数 = 6003 });
            res.Add(new { 日期 = "2024-04", 人数 = 9001 });
            res.Add(new { 日期 = "2024-05", 人数 = 3086 });
            var json = JsonConvert.SerializeObject(dynamic);
            LoggerHelper.WriteInfo("API接口", json);
            return Json(new { code = 0, msg = "操作成功", data = res });
        }

        public IActionResult UesdTest(string str)
        {
            var length = str.Length;
            var str2 = str.Substring(length - 10, 10);
            return Json(new { code = 0, msg = "操作成功", data = new { str = str, str2 = str2 } });

        }

        public async Task<IActionResult> Login(string UserName, string token)
        {
            if (!string.IsNullOrEmpty(UserName))
            {
                if (token != "" && true) //验证token，后期增加token的加密解密验证
                {
                    UserInfo user = await UserManager.FindByNameAsync(UserName);
                    await SignInManager.SignInAsync(user, isPersistent: false);

                    Save(db, HttpContext, user, user.SecurityStamp);
                    CommonFunction.LogLogin(db, HttpContext, user.UserName);
                    var expireTimeSpan = 30;
                    Int32.TryParse(config["AppSettings:ExpiredTime"], out expireTimeSpan);
                    // 身份验证成功，获取用户的ClaimsPrincipal对象
                    // var user = await _userManager.FindByNameAsync(model.Username);
                    var claimsPrincipal = await SignInManager.CreateUserPrincipalAsync(user);

                    // 自定义Cookie的过期时间
                    var authenticationProperties = new AuthenticationProperties
                    {
                        IsPersistent = true,
                        ExpiresUtc = DateTime.UtcNow.AddMinutes(expireTimeSpan),
                        //startup设置的只有60分钟 过了就会退出
                        // //因为ASP.NET Core有个硬性要求，是用户在超过50%的ExpiresUtc时间间隔内又访问了站点，才延长用户的登录时间。
                        // //如果AllowRefresh为false，表示用户登录后60分钟内不管有没有访问站点，只要60分钟到了，立马就处于非登录状态
                        AllowRefresh = true

                    };

                    await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, claimsPrincipal, authenticationProperties);

                    return Redirect("/Home/Index");
                }
            }
            ViewBag.SiteTitle = config["AppSettings:SiteTitle"];
            var pwd = GetMd5Hash("admin@123");
            //Console.WriteLine(pwd);
            return View();
        }

        public IActionResult NullPage()
        {
            return View();
        }

        public ActionResult GetValidateCode()
        {
            ValidateCode vCode = new ValidateCode();
            string code = vCode.CreateValidateCode(5);
            HttpContext.Session.SetString("ValidateCode_CHAT", code);
            byte[] content = vCode.CreateValidateGraphic(code);
            return File(content, @"image/jpeg");
        }

        [HttpPost]
        [AllowAnonymous]
        public async Task<JsonResult> Login(LoginViewModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {

                    var ValidateCode = HttpContext.Session.GetString("ValidateCode");
                    //if (ValidateCode != model.PassCode)
                    //{
                    //    return Json(new { errorMsg = "验证码不正确。" });
                    //}
                    UserInfo user = await UserManager.FindByNameAsync(model.UserName);
                    if (user != null)
                    {
                        if (user.StopUsing)
                        {
                            return Json(new { errorMsg = "账号已被停用。" });
                        }
                        //if (DynamicCode != "0")
                        //{
                        //    var isValidate = AuthenticatorService.Validate(model.DynamicCode, user.PSK);
                        //    if (!isValidate)
                        //    {
                        //        return Json(new { errorMsg = "动态口令错误，请重试！" });
                        //    }
                        //}

                        user.SecurityStamp = model.PassCode;
                        db.Entry(user).State = EntityState.Modified;
                        using (var transaction = db.Database.BeginTransaction())
                        {
                            try
                            {
                                db.SaveChanges();
                                transaction.Commit();
                            }
                            catch
                            {
                                transaction.Rollback();
                            }
                        }

                        user = await UserManager.FindByIdAsync(user.Id.ToString());
                        var result = await SignInManager.PasswordSignInAsync(user, model.Password, isPersistent: true, lockoutOnFailure: true);

                        var currentTimeOffset = DateTimeOffset.UtcNow;
                        if (result.Succeeded)
                        {
                            Save(db, HttpContext, user, user.SecurityStamp);
                            CommonFunction.LogLogin(db, HttpContext, user.UserName);
                            if (user.ErrorLoginCount > 0)
                            {
                                CommonFunction.AddErrorPwdCount(db, user.UserName, false);
                            }
                            HttpContext.Session.Set("UserInfo", user);
                            var leftDays = 0;
                            var UpdatePasDays = 0;
                            var WebConfigUpdatePasDays = db.WebConfigs.FirstOrDefault(o => o.WebKey.ToUpper() == "UPDATEPASDAYS");
                            if (WebConfigUpdatePasDays != null)
                            {
                                int.TryParse(WebConfigUpdatePasDays.WebValue, out UpdatePasDays);
                                if (UpdatePasDays != 0)
                                    leftDays = user.LastUpDatePwdTime.Subtract(System.DateTime.Now).Days;
                                if (leftDays * -1 <= UpdatePasDays) leftDays = 0;
                            }

                            var expireTimeSpan = 30;
                            Int32.TryParse(config["AppSettings:ExpiredTime"], out expireTimeSpan);
                            // 身份验证成功，获取用户的ClaimsPrincipal对象
                            // var user = await _userManager.FindByNameAsync(model.Username);
                            var claimsPrincipal = await SignInManager.CreateUserPrincipalAsync(user);

                            // 自定义Cookie的过期时间
                            var authenticationProperties = new AuthenticationProperties
                            {
                                IsPersistent = true,
                                ExpiresUtc = DateTime.UtcNow.AddMinutes(expireTimeSpan),
                                //startup设置的只有60分钟 过了就会退出
                                // //因为ASP.NET Core有个硬性要求，是用户在超过50%的ExpiresUtc时间间隔内又访问了站点，才延长用户的登录时间。
                                // //如果AllowRefresh为false，表示用户登录后60分钟内不管有没有访问站点，只要60分钟到了，立马就处于非登录状态
                                AllowRefresh = true

                            };

                            await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, claimsPrincipal, authenticationProperties);

                            return Json(new { okMsg = "/Home/Index", leftDay = leftDays, UpdatePasDays = UpdatePasDays });
                        }
                        else if (result.RequiresTwoFactor)
                        {
                            HttpContext.Response.StatusCode = 200; //不加这一句你，会返回302，HTTP/1.1 302 Found
                            return Json(new { twoFactorMsg = "/Account/SendCode" });
                        }
                        else if (result.IsLockedOut && (user.LockoutEnd == null || user.LockoutEnd > currentTimeOffset))
                        {
                            return Json(new { errorMsg = "账号已被锁定。" });
                        }
                        else
                        {
                            CommonFunction.AddErrorPwdCount(db, user.UserName, true);
                            return Json(new { errorMsg = "用户名或密码无效。" });
                        }
                    }
                    else
                    {
                        return Json(new { errorMsg = "用户名或密码无效。" });
                    }
                }
                catch (Exception ex)
                {
                    return Json(new { errorMsg = $"{ex.Message}-{ex?.InnerException?.Message}-{ex.StackTrace}" });
                }
            }
            else
            {
                return Json(new { errorMsg = "无效的参数。" });
            }
        }


        public void Save(AngelwinFollowUpDbContext db, HttpContext context, UserInfo user, string securityStamp)
        {
            //以下代码将权限保存到Session
            UserInfo? current_user = db.Users.AsNoTracking().Include(i => i.Roles).FirstOrDefault(i => i.Id == user.Id);
            // 获取服务提供者和配置
            var provider = context.RequestServices;
            //var appSettings = (IOptions<AppSettings>)provider.GetService(typeof(IOptions<AppSettings>));
            var appSettings = provider.GetService<IOptions<AppSettings>>();
            if (appSettings == null)
            {
                throw new InvalidOperationException("AppSettings 配置未找到，请检查依赖注入配置。");
            }



            string umlKey = "umlWeb_" + current_user?.UserName;
            var user_roles = current_user?.Roles.ToList();
            var rolesid = user_roles?.Select(c => c.RoleId).ToList() ?? [];
            List<RoleInfo> roles = db.Roles.AsNoTracking().Include(i => i.Menus).ThenInclude(c => c.Menu).Where(i => rolesid.Contains(i.Id)).ToList();

            var uml = new UserMenuList();

            foreach (var r in roles)
            {
                if (uml.Menus == null)
                {
                    //uml.Menus = r.Menus.Select(c => c.Menu).ToList();
                    uml.Menus = r.Menus != null ? r.Menus.Where(c => c.Menu != null).Select(c => c.Menu!).ToList() : new List<Menu>();
                }
                else
                {
                    if (uml.Menus != null && r.Menus != null)
                    {
                        //uml.Menus = uml.Menus.Union(r.Menus.Select(c => c.Menu).ToList());
                        uml.Menus = uml.Menus.Union(r.Menus.Select(c => c.Menu!).ToList()).ToList();
                    }
                    else if (uml.Menus == null && r.Menus != null)
                    {
                        //uml.Menus = r.Menus.Select(c => c.Menu).ToList();
                        uml.Menus = r.Menus.Select(c => c.Menu!).ToList();
                    }
                }
            }

            //var provider = context.RequestServices;
            //var appSettings = (IOptions<AppSettings>)provider.GetService(typeof(IOptions<AppSettings>));

            //if (appSettings.Value.OnlyAllowOnePlaceLogin)
            //{
            //    uml.SecurityStamp = securityStamp;
            //}

            //var cache = (IMemoryCache)provider.GetService(typeof(IMemoryCache));
            //cache.Set(umlKey, uml, new MemoryCacheEntryOptions()
            //{
            //    SlidingExpiration = TimeSpan.FromMinutes(appSettings.Value.ExpiredTime)
            //});
            // 将数据缓存到 MemoryCache
            //var cache = (IMemoryCache)provider.GetService(typeof(IMemoryCache));
            var cache = provider.GetService<IMemoryCache>()
            ?? throw new InvalidOperationException("IMemoryCache 服务未找到，请检查依赖注入配置。");
            // 验证 ExpiredTime 是否有效
            var expiredTime = appSettings.Value.ExpiredTime > 0 ? appSettings.Value.ExpiredTime : 30; // 默认 30 分钟

            if (expiredTime <= 0)
            {
                throw new InvalidOperationException("ExpiredTime must be a positive value.");
            }
            var cacheEntryOptions = new MemoryCacheEntryOptions
            {
                SlidingExpiration = TimeSpan.FromMinutes(expiredTime)
            };

            cache.Set(umlKey, uml, cacheEntryOptions);
        }

        public async Task<IActionResult> LogOff()
        {
            await SignInManager.SignOutAsync();
            return Redirect("/Account/Login");
        }
        string GetMd5Hash(string input)
        {
            using (MD5 md5Hash = MD5.Create())
            {
                byte[] data = md5Hash.ComputeHash(Encoding.UTF8.GetBytes(input));
                StringBuilder builder = new StringBuilder();

                for (int i = 0; i < data.Length; i++)
                {
                    builder.Append(data[i].ToString("x2"));
                }

                return builder.ToString();
            }
        }

        [Authorize]
        /// <summary>
        /// 重置密码
        /// </summary>
        /// <returns></returns>
        public IActionResult ResetPwd()
        {
            return View();
        }

        [Authorize]
        [Logging]
        public async Task<IActionResult> UpdatePwd(ChangePasswordViewModel model)
        {
            string currentUserName = User?.Identity?.Name;
            var user = db.Users.Include(i => i.Roles).FirstOrDefault(i => i.UserName == currentUserName);
            if (model.OldPassword == model.NewPassword)
            {
                return Json(new { code = -1, errorMsg = "新密码不能与当前密码相同，请重新输入" });
            }
            if (PasswordStrength(model.NewPassword) != Strength.Strong)
            {
                return Json(new { code = -1, errorMsg = "修改失败，密码最少8位，由数字、字母、符号组成" });
            }
            var result = await UserManager.ChangePasswordAsync(user, model.OldPassword, model.NewPassword);
            if (result.Succeeded)
            {
                user.LastUpDatePwdTime = DateTime.Now;//增加密码变更时间
                db.Users.Update(user);
                db.SaveChanges();
                await SignInManager.SignOutAsync();
                return Json(new { code = 0, okMsg = "密码修改成功！" });
            }
            else
            {
                return Json(new { code = -1, errorMsg = "密码修改失败。" });
            }
        }

        private static Strength PasswordStrength(string password)
        {

            //字符统计
            int iNum = 0, iLtt = 0, iSym = 0;
            foreach (char c in password)
            {
                if (c >= '0' && c <= '9') iNum++;
                else if (c >= 'a' && c <= 'z') iLtt++;
                else if (c >= 'A' && c <= 'Z') iLtt++;
                else iSym++;
            }
            if (iLtt == 0 && iSym == 0) return Strength.Weak; //纯数字密码
            if (iNum == 0 && iLtt == 0) return Strength.Weak; //纯符号密码
            if (iNum == 0 && iSym == 0) return Strength.Weak; //纯字母密码
            if (password.Length <= 6) return Strength.Weak; //长度不大于6的密码
            if (iLtt == 0) return Strength.Normal; //数字和符号构成的密码
            if (iSym == 0) return Strength.Normal; //数字和字母构成的密码
            if (iNum == 0) return Strength.Normal; //字母和符号构成的密码
            if (password.Length < 8) return Strength.Normal;//长度不大于10的密码
            return Strength.Strong; //由数字、字母、符号构成的密码
        }

        private enum Strength
        {
            Weak = 1, //低强度密码
            Normal = 2, //中强度密码
            Strong = 3 //高强度密码
        };
    }
#pragma warning disable
    public class LoginViewModel
    {
        [Display(Name = "用户名")]
        [Required]
        public string UserName { get; set; }
        [DataType(DataType.Password)]
        [Display(Name = "密码")]
        [Required]
        public string Password { get; set; }
        [Display(Name = "验证码")]
        [Required]
        public string PassCode { get; set; }
        [Display(Name = "记住我?")]
        public bool RememberMe { get; set; }
    }

    public class ChangePasswordViewModel
    {
        [DataType(DataType.Password)]
        [Display(Name = "当前密码")]
        [Required]
        public string OldPassword { get; set; }
        [DataType(DataType.Password)]
        [Display(Name = "新密码")]
        [Required]
        [StringLength(100, ErrorMessage = "{0} 必须至少包含 {2} 个字符。", MinimumLength = 6)]
        public string NewPassword { get; set; }
        [Compare("NewPassword", ErrorMessage = "新密码和确认密码不匹配。")]
        [DataType(DataType.Password)]
        [Display(Name = "确认新密码")]
        public string ConfirmPassword { get; set; }
    }
}
