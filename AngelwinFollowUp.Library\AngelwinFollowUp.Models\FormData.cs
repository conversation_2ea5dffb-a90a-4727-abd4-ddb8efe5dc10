﻿using AngelwinFollowUp.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AngelwinFollowUp.Models
{
    public class FormData
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        public string? CRFormId { get; set; }

        public string? CRFJsonValue { get; set; }

        /// <summary>
        /// AI提取原值json串
        /// </summary>
        public string? AIExtractJsonValue { get; set; }

        [MaxLength(50)]
        public string? CreateUserName { get; set; }
        public DateTime CreatedTime { get; set; }
    }
}
