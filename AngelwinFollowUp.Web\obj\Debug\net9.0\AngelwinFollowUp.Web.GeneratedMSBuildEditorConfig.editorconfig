is_global = true
build_property.TargetFramework = net9.0
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = AngelwinFollowUp.Web
build_property.RootNamespace = AngelwinFollowUp.Web
build_property.ProjectDir = D:\work space\project\三部\AI随访\AngelwinFollowUp.Web\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = D:\work space\project\三部\AI随访\AngelwinFollowUp.Web
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Areas/AIFollowUp/Views/FollowUpAI/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQUlGb2xsb3dVcFxWaWV3c1xGb2xsb3dVcEFJXEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Areas/AIFollowUp/Views/FollowupSummary/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQUlGb2xsb3dVcFxWaWV3c1xGb2xsb3d1cFN1bW1hcnlcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Areas/AIFollowUp/Views/FollowUpTemplates/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcQUlGb2xsb3dVcFxWaWV3c1xGb2xsb3dVcFRlbXBsYXRlc1xJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Areas/FollowUpManage/Views/FollowUpManageSearch/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcRm9sbG93VXBNYW5hZ2VcVmlld3NcRm9sbG93VXBNYW5hZ2VTZWFyY2hcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Areas/PatientManage/Views/PatientManage/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcUGF0aWVudE1hbmFnZVxWaWV3c1xQYXRpZW50TWFuYWdlXEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Views/Account/Login.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxMb2dpbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Views/Account/ResetPwd.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxSZXNldFB3ZC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Views/Home/changelog.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxjaGFuZ2Vsb2cuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Views/Home/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Views/Home/MenuHtmlPartialChild.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxNZW51SHRtbFBhcnRpYWxDaGlsZC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Views/Home/Privacy.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxQcml2YWN5LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Views/Home/Welcome.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxXZWxjb21lLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Views/Log/LoginLog.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcTG9nXExvZ2luTG9nLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Views/Log/Logs.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcTG9nXExvZ3MuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Views/Menu/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcTWVudVxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Views/Role/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUm9sZVxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Views/Shared/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXEVycm9yLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Views/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Views/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Views/User/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXNlclxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Views/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/work space/project/三部/AI随访/AngelwinFollowUp.Web/Views/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 
