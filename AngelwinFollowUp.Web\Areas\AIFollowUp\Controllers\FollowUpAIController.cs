﻿using AngelwinFollowUp.Web.Filters;
using AngelwinFollowUp.Models;
using Common.Tools;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SharpToken;
using System.Net.Http.Headers;
using System.Text;
using System.Text.RegularExpressions;
using System.Transactions;
using Microsoft.EntityFrameworkCore;

namespace AngelwinFollowUp.Web.Areas.AIFollowUp.Controllers
{
    [Authorizing]
    [Area("AIFollowUp")]
    public class FollowUpAIController : Controller
    {
        private IConfiguration Configuration { get; }

        private readonly AngelwinFollowUpDbContext db;
        public FollowUpAIController(IConfiguration configuration, AngelwinFollowUpDbContext _db)
        {
            Configuration = configuration;
            db = _db;
        }

        // GET: FollowUpAIController
        public ActionResult Index(int patientId, int templateId, int typeId)
        {

            ViewBag.XuFeiAPPID = Configuration["AppSettings:XuFeiAPPID"];
            ViewBag.XuFeiAPI_KEY = Configuration["AppSettings:XuFeiAPI_KEY"];
            ViewBag.XuFei_TTS_APPID = Configuration["AppSettings:XuFei_TTS_APPID"];
            ViewBag.XuFei_TTS_API_KEY = Configuration["AppSettings:XuFei_TTS_API_Key"];
            ViewBag.XuFei_TTS_API_SECRET = Configuration["AppSettings:XuFei_TTS_API_Secret"];
            ViewBag.PatientId = patientId;
            ViewBag.TemplateId = templateId;
            ViewBag.TypeId = typeId;
            ViewBag.TypeName = db.AITaskTypes.FirstOrDefault(d => d.Id == typeId)?.TypeName;
            var patient = db.ResearchPatients.Include(d => d.HospitalDept).FirstOrDefault(a => a.Id == patientId);
            return View(patient);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        //public ActionResult Details()
        //{
        //    var mList = db.ResearchPatients.ToList().Select(p => new
        //    {
        //        PatientName = p.PatientName
        //    }).ToList();
        //    return Json(mList);
        //}

        /// <summary>
        /// 获取患者的随访记录列表
        /// </summary>
        /// <param name="patientId">患者Id</param>
        /// <returns>随访记录列表</returns>
        public ActionResult GetFollowupRecords(int patientId, int templateId)
        {
            var followupRecords = from plan in db.FollowupPlans
                                  join detail in db.FollowupTemplateDetails on plan.TemplateDetailId equals detail.Id
                                  join record in db.FollowupRecords on plan.Id equals record.FollowupPlanId into records
                                  from re in records.DefaultIfEmpty()
                                  where plan.PatientId == patientId && detail.TemplateId == templateId
                                  && plan.Status != -1
                                  orderby detail.Orderby
                                  select new
                                  {
                                      plan.Id,
                                      plan.TemplateDetailId,
                                      plan.PatientId,
                                      plan.Description,
                                      plan.Status,
                                      plan.StatusTime,
                                      detail.TemplateDetailName,
                                      detail.FollowupPoint,
                                      detail.Orderby,
                                      FollowupDate =Convert.ToDateTime(detail.FollowupPoint).ToString("yyyy-MM-dd"),
                                      RecordId = re != null ? re.Id : 0
                                  };

            return Json(followupRecords.ToList());
        }

        /// <summary>
        /// 获取随访记录详情
        /// </summary>
        /// <param name="followupRecordId">随访记录Id</param>
        /// <returns>随访记录详情</returns>
        public ActionResult GetFollowupRecordDetails(int followupRecordId)
        {
            var followupRecordDetails = from detail in db.FollowupRecordDetails
                                        join record in db.FollowupRecords on detail.FollowupRecordId equals record.Id
                                        where detail.FollowupRecordId == followupRecordId
                                        orderby detail.CreatedTime
                                        select new
                                        {
                                            detail.Content,
                                            detail.CreateUserName,
                                            detail.CreatedTime,
                                            record.Id,
                                            record.PatientId,
                                            record.FollowupPlanId,
                                            record.TemplateDetailId,
                                            record.ModelName,
                                            record.Remark
                                        };

            return Json(followupRecordDetails.ToList());
        }

        /// <summary>
        /// 校验随访详情是否有数据
        /// </summary>
        /// <param name="followupRecordId"></param>
        /// <returns></returns>
        public IActionResult CheckRecordDetailData(int followupRecordId)
        {
            var flag = db.FollowupRecordDetails.Any(o => o.FollowupRecordId == followupRecordId);
            return Json(flag);
        }

        /// <summary>
        /// 获取随访模版内容
        /// </summary>
        /// <param name="followupRecordId"></param>
        /// <returns></returns>
        public IActionResult GetTemplatesContent(int followupRecordId)
        {
            var data = db.FollowupRecords.Include(d => d.FollowupPlan).Where(o => o.Id == followupRecordId).Select(d => d.FollowupPlan.Description).FirstOrDefault();
            return Json(data);
        }

        [SSE]
        public async Task<ActionResult> GetChatStreamAnswer(int followupRecordId, int planId,int patientId,int templateDetailId, string promptTemp, string prompt)
        {
            LoggerHelper.WriteInfo("其他日志", $"GetChatStramAnswer:开始");
            var response = Response;
            response.Headers.Append("Cache-Control", "no-cache");
            response.Headers.Append("Connection", "keep-alive");
            try
            {
                string modelType = Configuration["AppSettings:modelType"] ?? string.Empty;
                var patientInfo = db.ResearchPatients.Include(p => p.HospitalDept).FirstOrDefault(d => d.Id == patientId);
                if (patientInfo == null)
                {
                    throw new InvalidOperationException($"未找到 Id 为 {patientId} 的患者信息。");
                }

                promptTemp = db.FollowupPlans.Where(o => o.Id == planId).Select(d => d.Description).FirstOrDefault() ?? string.Empty;
                if (string.IsNullOrEmpty(promptTemp))
                {
                    throw new InvalidOperationException($"未找到 Id 为 {planId} 的随访计划描述。");
                }

                // 构建患者基本信息
                string patientBasicInfo = $@"
                                    {patientInfo.PatientName} {patientInfo.Sex}
                                    编号：{patientInfo.PatientId}
                                    科室：{patientInfo.HospitalDept?.Name ?? "未知科室"}
                                    临床诊断：{patientInfo.Diagnosis}
                                    ";
                // 定义提示词
                string promptTemplate = string.Format(@"
                        <角色>
                        角色：你是一位经验丰富的随访医生。
                        </角色>
                        <任务>
                        任务：根据患者的病情和随访内容，对患者进行系统的随访，确保每个问题都被详细询问并记录患者的回答。
                        </任务>
                        <背景>
                        背景：在医疗随访过程中，医生需要根据患者的具体病情和预设的随访内容，逐一向患者提问，确保全面了解患者的状况，并在必要时提供适当的建议和安慰。
                        </背景>
                        <执行要求>
                        执行要求：
                        1. 仔细阅读并理解患者的病情和随访内容。
                        2. 逐一向患者提问，确保每个问题都被问到。
                        3. 记录患者的回答，避免重复提问。
                        4. 在患者回答中若发现对病情不利的情况，适当给予安慰或建议。
                        5. 当患者问及其他医疗事宜时，建议患者咨询医生。
                        6. 随访结束后，告知患者随访已完成并表达感谢和祝福。
                        </执行要求>
                        <输出要求>
                        输出要求：
                        1. 输出格式为逐个提问的问题，每个问题前可加拟人化的语气词。
                        2. 问题之间逻辑清晰，结构严谨。
                        3. 在必要时，问题后可附上适当的安慰或建议。
                        </输出要求>
                        <输入>
                        用户输入：你是一个经验丰富的随访医生，请根据患者基本情况【{0}】以及随访内容【{1}】，对患者进行随访。你需要熟读随访内容中的每个问题，逐一向患者提问，并记住患者回答的内容，在上下文中已经随访的问题不用再次随访，直到所有的问题随访完毕后，告知患者“本次随访结束，谢谢您的配合，祝您身体健康”。每次只生成一个提问问题，可以在生成的问题前后增加一些拟人化的语气词，让患者更加容易接受，如果患者回答的问题有对病情不好的地方，可以适当安慰下患者，或给出一些你作为医生的建议。
                        </输入>", patientBasicInfo, promptTemp);
                
                if (string.IsNullOrEmpty(prompt))
                {
                    await response.WriteAsync($"event: end\ndata:{JsonConvert.SerializeObject(new { errorMsg = "你没有提交任何问题！" })}\n\n");
                    await response.Body.FlushAsync();
                    return new EmptyResult();
                }

                #region 验证关键字
                var filterKeywordList = CheckKeyWords(prompt);
                if (filterKeywordList.Any())
                {
                    var errorMsg = $"含有敏感信息：{string.Join(",", filterKeywordList.ToArray())},禁止提交！";
                    await response.WriteAsync($"event: end\ndata:{JsonConvert.SerializeObject(new { errorMsg = errorMsg })}\n\n");
                    await response.Body.FlushAsync();
                    return new EmptyResult();
                }
                #endregion

                #region 读取配置文件
                string apiKey = Configuration[$"GPTSetting:{modelType}:ApiKey"]??string.Empty;
                string model = Configuration[$"GPTSetting:{modelType}:Model"] ?? string.Empty;
                string apiUrl = Configuration[$"GPTSetting:{modelType}:apiUrl"] ?? string.Empty;
                int maxTokens = int.TryParse(Configuration[$"GPTSetting:{modelType}:MaxTokens"], out var tokens) ? tokens : 4000;
                #endregion

                #region 获取历史对话记录
                dynamic history = new List<dynamic>();
                int historyTokens = 0;
                if (followupRecordId > 0)
                {
                    var RecordDetailList = db.FollowupRecordDetails.Where(o => o.FollowupRecordId == followupRecordId)
                        .OrderByDescending(o => o.CreatedTime).ToList();
                    if (RecordDetailList != null && RecordDetailList.Any())
                    {
                        var tokenCount = RecordDetailList.Sum(o => o.TokenCount);
                        if (tokenCount < maxTokens)
                        {
                            history = RecordDetailList.OrderBy(o => o.CreatedTime)
                                .Select(o => new { role = o.CreateUserName, content = o.Content }).ToList();
                            historyTokens = tokenCount;
                        }
                    }
                    else { 
                    
                    }
                }
                #endregion

                #region 调用大模型API
                var httpClient = new HttpClient(new HttpClientHandler());
                httpClient.BaseAddress = new Uri(apiUrl);
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                // 提示词模板始终是第一条记录
                var msgs = new List<object>
                {
                    new { 
                        role = "user",
                        content = promptTemplate
                    } 
                };
                msgs.AddRange(history);
                msgs.Add(new { role = "user", content = prompt }); // 患者回答的内容

                var requestBody = JsonConvert.SerializeObject(new
                {
                    model,
                    messages = msgs,
                    stream = true
                });

                var request = new HttpRequestMessage(HttpMethod.Post, "chat/completions")
                {
                    Content = new StringContent(requestBody, Encoding.UTF8, "application/json")
                };

                var responseContent = new StringBuilder();
                using (var APIResponse = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead))
                {
                    if (APIResponse.IsSuccessStatusCode)
                    {
                        var stream = await APIResponse.Content.ReadAsStreamAsync();
                        using (var streamReader = new StreamReader(stream))
                        {
                            while (!streamReader.EndOfStream)
                            {
                                var line = await streamReader.ReadLineAsync();
                                if (!string.IsNullOrWhiteSpace(line) && line != "data: [DONE]")
                                {
                                    if (line.StartsWith("data:")) line = line.Substring(5);
                                    var delta = JObject.Parse(line).SelectToken("choices")?.First().SelectToken("delta");
                                    if (delta?["content"] != null)
                                    {
                                        var contentStr =delta["content"]?.ToString();
                                        responseContent.Append(contentStr);
                                        await response.WriteAsync($"data:{JsonConvert.SerializeObject(new { okMsg = "成功", data = contentStr })}\n\n");
                                        await response.Body.FlushAsync();
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        var errorMsg = "Failed to connect to API.";
                        await response.WriteAsync($"event: end\ndata:{JsonConvert.SerializeObject(new { errorMsg = errorMsg })}\n\n");
                        await response.Body.FlushAsync();
                        return new EmptyResult();
                    }
                }
                #endregion

                #region 结果插入数据库
                FollowupRecord followupRecord;
                using (var trans = new TransactionScope())
                {
                    followupRecord = db.FollowupRecords.FirstOrDefault(o => o.Id == followupRecordId) ?? new FollowupRecord
                    {
                        PatientId = patientId,
                        FollowupPlanId = planId,
                        TemplateDetailId = templateDetailId,
                        ModelName = modelType ?? "",
                        Remark = "AI助手随访记录",
                        CreatedTime = DateTime.Now,
                        CreateUserName = User?.Identity?.Name
                    };
                    if (followupRecord.Id == 0) db.Add(followupRecord);
                    db.SaveChanges();

                    // 插入随访记录详细表
                    var followupRecordDetails = new List<FollowupRecordDetail>();
                    if (prompt == "开始随访")
                    {
                        followupRecordDetails.Add(new FollowupRecordDetail
                        {
                            FollowupRecordId = followupRecord.Id,
                            Content = responseContent.ToString(),
                            TokenCount = GptEncoding.GetEncoding("cl100k_base").Encode(responseContent.ToString()).Count,
                            CreateUserName = "assistant",
                            CreatedTime = DateTime.Now
                        });

                    }
                    else
                    {
                        followupRecordDetails.Add(new FollowupRecordDetail
                        {
                            FollowupRecordId = followupRecord.Id,
                            Content = prompt,
                            TokenCount = GptEncoding.GetEncoding("cl100k_base").Encode("{\"role\":\"user\",\"content\":\"" + prompt + "\"}").Count,
                            CreateUserName = "user",
                            CreatedTime = DateTime.Now
                        });
                        followupRecordDetails.Add(new FollowupRecordDetail
                        {
                            FollowupRecordId = followupRecord.Id,
                            Content = responseContent.ToString(),
                            TokenCount = GptEncoding.GetEncoding("cl100k_base").Encode(responseContent.ToString()).Count,
                            CreateUserName = "assistant",
                            CreatedTime = DateTime.Now
                        });
                    }
                    db.AddRange(followupRecordDetails);
                    db.SaveChanges();

                    var planInfo = db.FollowupPlans.FirstOrDefault(d => d.Id == planId);
                    if (planInfo != null && followupRecordDetails != null && followupRecordDetails.Any())
                    {
                        var endFlag = followupRecordDetails.Any(d => d.Content?.Contains("本次随访结束") ?? false);
                        if (endFlag)
                        {
                            planInfo.Status = 1;
                            planInfo.StatusTime = DateTime.Now;
                        }
                    }
                    db.SaveChanges();
                    trans.Complete();
                }
                #endregion

                LoggerHelper.WriteInfo("其他日志", $"GetChatAnswer:结束");
                var resultAll = new
                {
                    okMsg = $"成功",
                    recordId= followupRecord.Id,
                    role = "assistant",
                    content = responseContent.ToString()
                };
                await response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(resultAll)}\n\n");
                await response.Body.FlushAsync();
                return new EmptyResult();
            }
            catch (Exception ex)
            {
                await response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(new { errorMsg = $"报错" + ex.Message })}\n\n");
                await response.Body.FlushAsync();
                return new EmptyResult();
            }
        }


        public List<string> CheckKeyWords(string prompt)
        {
            List<string> matchedKeywords = new List<string>();
            try
            {
                var keywords = Configuration["GPTSetting:FilterKeywords"];
                var keywordArrys = keywords?.Split(',');
                if (keywordArrys != null && keywordArrys.Length > 0)
                {
                    foreach (string keyword in keywordArrys)
                    {
                        string pattern = $@"{Regex.Escape(keyword)}";
                        if (Regex.IsMatch(prompt, pattern, RegexOptions.IgnoreCase))
                        {
                            matchedKeywords.Add(keyword);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                matchedKeywords.Add(ex.Message);
            }
            return matchedKeywords;
        }
    }
}
