﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AngelwinFollowUp.Models
{
    /// <summary>
    /// CRF表单变量字段设置 addbyzolf 20250709
    /// 调整：跟科研AI比，字段只跟anyreport中的formId相关，不在跟系统中CRForm表的自增id相关，避免重复使用的表单需要重复录入该表字段
    /// </summary>
    public partial class CRFormFieldSet
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string FormId { get; set; } = null!;

        [Required]
        [MaxLength(200)]
        public string FormName { get; set; } = null!;//表单名称

        [MaxLength(500)]
        public string FieldName { get; set; } = null!;//字段名称
        [MaxLength(500)]
        public string FieldComment { get; set; } = null!;//字段名称

        [DefaultValue(1)]
        public int Orderby { get; set; }  //排序号
        [MaxLength(1000)]
        public string ExtractSet { get; set; } = null!;//提取配置

        [MaxLength(50)]
        public string MinRangeValue { get; set; } = null!;//最小区间值 

        [MaxLength(50)]
        public string MaxRangeValue { get; set; } = null!;//最大区间值

        [MaxLength(50)]
        public string CreateUserName { get; set; } = null!;
        public DateTime CreatedTime { get; set; }
    }
}
