﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;

namespace AngelwinFollowUp.Models
{
    public partial class FollowupRecordDetail
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        [ForeignKey("FollowupRecord")]
        public int FollowupRecordId { get; set; } // 随访记录Id
        public required string Content { get; set; } = string.Empty;// 问答内容
        [DefaultValue(0)]
        public int TokenCount { get; set; }  // token数
        [MaxLength(100)]
        public string CreateUserName { get; set; } = string.Empty; // 创建人（assistant 或 patient）
        public  DateTime CreatedTime { get; set; } = System.DateTime.Now; // 创建时间
        public virtual FollowupRecord FollowupRecord { get; set; } = null!; // 导航属性，关联 FollowupRecords 表
    }
}