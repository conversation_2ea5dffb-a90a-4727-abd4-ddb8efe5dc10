﻿using AngelwinFollowUp.Models;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AngelwinFollowUp.Models
{
    public partial class FollowupRecord
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [ForeignKey("ResearchPatient")]
        public int PatientId { get; set; } // 患者Id
        public virtual ResearchPatient ResearchPatient { get; set; } = null!;

        [ForeignKey("FollowupPlan")]
        public int FollowupPlanId { get; set; } // 随访计划Id

        [ForeignKey("FollowupTemplateDetail")]
        public int TemplateDetailId { get; set; } // 随访模版明细Id

        [MaxLength(100)]
        public string? ModelName { get; set; } // 大模型名称

        [MaxLength(2000)]
        public string? Remark { get; set; } // 备注

        [MaxLength(100)]
        public string CreateUserName { get; set; } = string.Empty;  // 创建人
        public DateTime CreatedTime { get; set; } = System.DateTime.Now; // 创建时间
        public virtual FollowupPlan FollowupPlan { get; set; } = null!; // 导航属性，关联 FollowupPlans 表

        public virtual FollowupTemplateDetail FollowupTemplateDetail { get; set; } = null!; // 导航属性，关联 FollowupTemplateDetails 表
    }
}