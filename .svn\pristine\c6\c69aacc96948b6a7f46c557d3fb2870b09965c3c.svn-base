﻿using AngelwinFollowUp.Models;
using AngelwinFollowUp.Web.Filters;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Dynamic;
using System.Globalization;
using System.Text.Json;

namespace AngelwinFollowUp.Web.Areas.PatientManage.Controllers
{
    [Authorizing]
    [Area("PatientManage")]
    public class PatientManageController : Controller
    {
        private IConfiguration Configuration { get; }

        private readonly AngelwinFollowUpDbContext db;
        public PatientManageController(IConfiguration configuration, AngelwinFollowUpDbContext _db)
        {
            Configuration = configuration;
            db = _db;
        }
        public IActionResult Index()
        {
            return View();
        }

        public class FormItem
        {
            public int Id { get; set; }
            public string Name { get; set; }
        }

        public IActionResult List(int page, int limit, int Deps,string keyWord)
        {
            var totalCount = 0;
            var resultResult = new List<dynamic>();
            var query = db.ResearchPatients.AsQueryable();
            if (Deps > 0)
            {
                query = query.Where(o => o.HospitalDeptId == Deps).AsQueryable();
            }
            if (!string.IsNullOrWhiteSpace(keyWord))
            {
                keyWord = keyWord.Trim();
                query = query.Where(o => o.PatientId.Contains(keyWord) || o.PatientName.Contains(keyWord)).AsQueryable();
            }
            totalCount = query.Count();
            var list = query.OrderBy(o => o.Id).Skip((page - 1) * limit).Take(limit).ToList();
            if (list != null && list.Any())
            {
                foreach (var item in list)
                {
                    var DepName = "";
                    if (item.HospitalDeptId > 0)
                    {
                        var data = db.HospitalDepts.Where(o => o.IsUsed == true && o.Id == item.HospitalDeptId).FirstOrDefault();
                        DepName = data?.Name;
                    }
                    dynamic result = new ExpandoObject();
                    result.Id = item.Id;
                    result.PatientId = item.PatientId;
                    result.PatientName = item.PatientName;
                    result.PatientSource = item.PatientSource;
                    result.IDCardNo = item.IDCardNo;
                    result.Telephone = item.Telephone;
                    result.Sex = item.Sex;
                    result.Age = item.Age;
                    result.BrithDay = item.BrithDay?.ToString("yyyy-MM-dd");
                    result.BRKH = item.BRKH;
                    result.BLH = item.BLH;
                    result.Diagnosis = item.Diagnosis;
                    result.ICDCode = item.ICDCode;
                    result.HospitalDeptName = DepName;
                    result.HospitalDeptId = item.HospitalDeptId;
                    result.CreateUserName = item.CreateUserName;
                    result.CreatedTime = item.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss");
                    resultResult.Add(result);
                }
            }
            // 使用 System.Text.Json 的配置
            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = null // 保持属性名称原样输出（默认行为）
            };
            return Json(new { code = "0", msg = "成功", count = totalCount, data = resultResult }, options);
        }

        [HttpPost]
        public IActionResult Save(ResearchPatient node)
        {
            try
            {
                var msg = "";
                if (node.PatientId == null)
                {
                    return Json(new { code = -1, errorMsg = "患者ID不能为空！" });
                }
                if (node.Id == 0)
                {
                    node.CreateUserName = User.Identity.Name + "";
                    node.Diagnosis = node.Diagnosis + "";
                    node.Sex = node.Sex == "0" ? "女" : "男";
                    db.ResearchPatients.Add(node);
                    msg = "创建成功。";
                }
                else
                {
                    var entity = db.ResearchPatients.FirstOrDefault(o => o.Id == node.Id);
                    if (entity == null)
                        return Json(new { okMsg = "数据不存在！" });
                    entity.PatientId = node.PatientId;
                    entity.PatientSource = node.PatientSource;
                    entity.PatientName = node.PatientName;
                    entity.IDCardNo = node.IDCardNo + "";
                    entity.Telephone = node.Telephone + "";
                    entity.Sex = node.Sex == "0" ? "女" : "男";
                    entity.BrithDay = node.BrithDay;
                    entity.BRKH = node.BRKH + "";
                    entity.BLH = node.BLH + "";
                    entity.Diagnosis = node.Diagnosis + "";
                    entity.ICDCode = node.ICDCode + "";
                    entity.HospitalDeptId = node.HospitalDeptId;
                    entity.CreateUserName = User.Identity.Name;
                    entity.CreatedTime = System.DateTime.Now;
                    msg = "修改成功。";
                }
                db.SaveChanges();
                return Json(new { code = 0, okMsg = msg });
            }
            catch (System.Exception ex)
            {
                return Json(new { code = -1, errorMsg = "保存数据时出错。" + ex.Message });
            }
        }


        [HttpPost]
        public IActionResult PlanSave(FollowupPlan node)
        {
            try
            {
                var msg = "";
                var lis = node.Description.Split(',');
                for (int i = 0; i < lis.Length; i++)
                {
                    var Plan = db.FollowupPlans.Where(o=> o.TemplateId == node.TemplateId && o.TemplateDetailId == int.Parse(lis[i]) && o.PatientId == node.PatientId).Count();
                    if (Plan > 0 )
                    {
                        msg = "随访计划已存在。";
                    }
                    else
                    {
                        var TDetail = db.FollowupTemplateDetails.Where(a => a.TemplateId == node.TemplateId && a.Id == int.Parse(lis[i])).FirstOrDefault();
                        
                            node.CreateUserName = User.Identity.Name + "";
                            node.Description = TDetail.Description + "";
                            node.TemplateDetailId = int.Parse(lis[i]);
                            node.Remark = node.Remark + "";
                            node.StatusTime = DateTime.Now;
                            node.Id = 0;
                            db.FollowupPlans.Add(node);
                            msg = "创建成功。";
                            db.SaveChanges();

                    }

                }
                return Json(new { code = 0, okMsg = msg });
            }
            catch (System.Exception ex)
            {
                return Json(new { code = -1, errorMsg = "保存数据时出错。" + ex.Message });
            }
        }

        [HttpPost]
        public IActionResult Del(int id)
        {
            try
            {
                var node = db.ResearchPatients.Where(a => a.Id == id).FirstOrDefault();

                db.Entry(node).State = EntityState.Deleted;
                db.SaveChanges();
                return Json(new { okMsg = "删除成功。" });
            }
            catch (Exception ex)
            {
                var error = ex.InnerException?.Message;
                return Json(new { errorMsg = $"该患者已生成随访计划，不能删除。请联系管理员！" });
            }
        }

        public IActionResult GetDeptList()
        {
            List<FormItem> formList = new List<FormItem>();
            FormItem fromItems = new FormItem();
            var data = db.HospitalDepts
                .Where(o => o.IsUsed == true)
               .Select(o => new FormItem
               {
                   Id = o.Id,
                   Name = o.Name
               }).ToList();
            return Json(new { code = 0, msg = "提取成功！", data = data });
        }

        public IActionResult GetTempList()
        {
            List<FormItem> formList = new List<FormItem>();
            FormItem fromItems = new FormItem();
            var data = db.FollowupTemplates
               .Select(o => new FormItem
               {
                   Id = o.Id,
                   Name = o.TemplateName
               }).ToList();
            return Json(new { code = 0, msg = "提取成功！", data = data });
        }

        public IActionResult GetTempDetailList(int Id)
        {
            List<FormItem> formList = new List<FormItem>();
            FormItem fromItems = new FormItem();
            var data = db.FollowupTemplateDetails
                 .Where(o => o.TemplateId == Id)
               .Select(o => new FormItem
               {
                   Id = o.Id,
                   Name = o.TemplateDetailName
               }).ToList();
            return Json(new { code = 0, msg = "提取成功！", data = data });
        }
    }
}
