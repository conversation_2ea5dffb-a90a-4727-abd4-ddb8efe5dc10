﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AngelwinFollowUp.Models
{
    /// <summary>
    /// AI任务类型 addbyzolf 20250709
    /// </summary>
    public partial class AITaskType
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string TypeName { get; set; } = string.Empty; // 随访内容

        [MaxLength(2000)]
        public string Intro { get; set; } = string.Empty; // 随访内容
    }
}
